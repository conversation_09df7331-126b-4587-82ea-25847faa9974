#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

"""
ContentWidget avec NanoContainer intégré
Hérite de ContentWidget original et remplace le contenu par un NanoContainer
"""

from nano_dashboard import ContentWidget
from nano_container import <PERSON><PERSON><PERSON><PERSON>bChartContainer, NanoContainer
from nano_analyses import MarketAnalyzer
from nano_ai_analyzer import NanoAIAnalyzer
from nano_data_audit import global_auditor
from PySide6.QtWidgets import QVBoxLayout
from PySide6.QtCore import QTimer
import functools

def audit_data_function(func):
    """Décorateur pour auditer automatiquement les fonctions d'affichage de données"""
    @functools.wraps(func)
    def wrapper(self, analyzer, *args, **kwargs):
        # Auditer la fonction avec le système global
        audit_result = global_auditor.audit_function(func, analyzer, *args, **kwargs)

        # Afficher les recommandations si nécessaire
        if audit_result.recommendations:
            print(f"📋 Recommandations pour {func.__name__}:")
            for rec in audit_result.recommendations:
                print(f"  • {rec}")

        return audit_result
    return wrapper

class ContentWidgetWithNanoContainer(ContentWidget):
    """ContentWidget original avec NanoContainer comme contenu et audit intégré"""

    def __init__(self, dark_mode=True, parent=None, container_config=None):
        # Configuration du container AVANT l'appel au parent
        self.container_config = container_config or {
            "title": "Container",
            "type": "normal",
            "analysis": "generic",
            "status": "neutral"
        }

        # Initialiser l'historique d'audit
        self.audit_history = []

        # Pré-initialiser le nano_container pour éviter les erreurs
        self.nano_container = None

        # Appeler le constructeur parent
        super().__init__(dark_mode, parent)

        # Créer et intégrer le NanoContainer APRÈS l'initialisation du parent
        self.setup_nano_container()
        
    def setup_nano_container(self):
        """Remplace le contenu du body par un NanoContainer"""

        # Vérifier que le body existe
        if not hasattr(self, 'body') or self.body is None:
            print("⚠️ Body non initialisé, report de la création du nano_container")
            return

        # Vider le body existant
        layout = self.body.layout()
        if layout:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

        try:
            # Créer le bon type de container
            if self.container_config["type"] == "plotly":
                # Déterminer si on doit afficher un graphique par défaut
                analysis_type = self.container_config.get("analysis", "generic")
                show_default = analysis_type not in ["forecast", "sentiment", "momentum", "correlation"]
                self.nano_container = MatplotlibChartContainer(self, self.dark_mode, show_default)
            elif self.container_config["type"] == "notifications":
                # Créer un container spécial pour les notifications
                from nano_notifications_page import NotificationsPage
                self.nano_container = NotificationsPage(parent=self)
            else:
                self.nano_container = NanoContainer(self, self.dark_mode)

            # Configuration initiale (seulement pour les containers normaux)
            if self.container_config["type"] != "notifications":
                self.nano_container.set_banner_status(
                    self.container_config["status"],
                    self.container_config["title"]
                )
                self.nano_container.set_phrase("Initialisation...")

            # Ajouter au body
            if layout:
                layout.addWidget(self.nano_container)

            # Stocker la référence pour accès externe
            self.container_config["nano_container"] = self.nano_container

            print(f"✅ NanoContainer créé: {self.container_config['title']}")

        except Exception as e:
            print(f"⚠️ Erreur création NanoContainer: {e}")
            self.nano_container = None
        
    def update_dark_mode(self, dark_mode: bool):
        """Met à jour le mode sombre/clair"""
        # Appeler la méthode parent
        super().update_dark_mode(dark_mode)

        # Mettre à jour le nano container seulement s'il existe et n'est pas None
        if hasattr(self, 'nano_container') and self.nano_container is not None:
            self.nano_container.update_dark_mode(dark_mode)
    
    def get_nano_container(self):
        """Retourne le NanoContainer pour accès externe"""
        if hasattr(self, 'nano_container') and self.nano_container is not None:
            return self.nano_container
        return None
    
    def update_container_data(self, analyzer: MarketAnalyzer):
        """Met à jour les données du container selon son type d'analyse"""
        if not self.nano_container:
            return

        analysis_type = self.container_config.get("analysis", "generic")
        
        # Dispatcher vers la bonne méthode d'analyse
        if analysis_type == "dominance":
            self._update_dominance_analysis(analyzer)
        elif analysis_type == "altseason":
            self._update_altseason_analysis(analyzer)
        elif analysis_type == "risk":
            self._update_risk_analysis(analyzer)
        elif analysis_type == "volume":
            self._update_volume_analysis(analyzer)
        elif analysis_type == "marketcap":
            self._update_marketcap_analysis(analyzer)
        elif analysis_type == "capital_flows":
            self._update_capital_flows_analysis(analyzer)
        elif analysis_type == "orderbook":
            self._update_orderbook_analysis(analyzer)
        elif analysis_type == "vol_dominance":
            self._update_vol_dominance_analysis(analyzer)
        elif analysis_type == "correlation":
            self._update_correlation_analysis(analyzer)
        elif analysis_type == "forecast":
            self._update_forecast_analysis(analyzer)
        elif analysis_type == "sentiment":
            self._update_sentiment_analysis(analyzer)
        elif analysis_type == "momentum":
            self._update_momentum_analysis(analyzer)
        elif analysis_type == "ai_analysis":
            self._update_ai_analysis(analyzer)
        elif analysis_type == "notifications":
            self._update_notifications_analysis(analyzer)
        else:
            # Analyse générique par défaut
            self._update_generic_analysis(analyzer)
    
    @audit_data_function
    def _update_dominance(self, analyzer):
        """Met à jour le container de dominance avec donut chart"""
        import pandas as pd  # Import ajouté pour gérer les Timedelta
        from datetime import datetime  # Pour la gestion des dates

        try:
            # PRIORITÉ 1: Récupérer les données les plus récentes directement de la base
            latest_data = analyzer.db.get_latest_market_data(limit=1)

            if not latest_data.empty:
                # Utiliser les données fraîches de la base (DERNIÈRE LIGNE)
                btc_dom_fresh = float(latest_data.iloc[0]['btc_dom'])
                print(f"🔄 Données EXACTES de la base (row {latest_data.index[0]}): BTC Dom: {btc_dom_fresh:.10f}% → Affiché: {btc_dom_fresh:.2f}%")
            else:
                btc_dom_fresh = None
                print("⚠️ Aucune donnée fraîche disponible")

            # Récupérer l'évolution sur 1 semaine pour le graphique et les variations
            dom_evolution = analyzer.get_dominance_evolution(hours=168)  # 1 semaine

            if dom_evolution.empty:
                raise ValueError("Aucune donnée de dominance disponible")
            
            # S'assurer que created_at est au format datetime
            if not pd.api.types.is_datetime64_any_dtype(dom_evolution['created_at']):
                dom_evolution['created_at'] = pd.to_datetime(dom_evolution['created_at'])
                
            # Vérifier que nous avons des dates valides
            if dom_evolution['created_at'].isnull().any():
                raise ValueError("Données de date invalides dans les données de dominance")
            
            # Utiliser la nouvelle fonction spécialisée pour calculer les variations de dominance
            variations = analyzer.calculate_dominance_variations()
            print(f"🔄 Variations dominance calculées: {variations}")
            
            # Utiliser les données fraîches en priorité, sinon fallback sur évolution
            if btc_dom_fresh is not None:
                btc_dom = btc_dom_fresh
                print(f"✅ Utilisation données fraîches: {btc_dom:.2f}%")
            else:
                # Fallback sur la dernière valeur de l'évolution
                latest_dom = dom_evolution.iloc[-1]
                btc_dom = latest_dom['btc_dom']
                print(f"⚠️ Utilisation données évolution: {btc_dom:.2f}%")
            
            # Mettre à jour le banner avec la valeur actuelle
            status, phrase = analyzer.analyze_dominance_trend()
            print(f"🔄 Phrase générée: {phrase}")
            self.nano_container.set_banner_status(status, f"🟡 BTC Dom: {btc_dom:.2f}%")
            self.nano_container.set_phrase(phrase)
            
            # Mettre à jour le tableau des variations
            self.nano_container.update_variations(variations)

            # Afficher le graphique d'évolution avec données fraîches
            if hasattr(self.nano_container, 'create_dominance_donut_chart'):
                # Utiliser les données fraîches pour le graphique aussi
                try:
                    fresh_data = analyzer.db.get_latest_market_data(limit=50)  # 50 dernières entrées
                    if not fresh_data.empty:
                        fresh_data_reset = fresh_data.reset_index()
                        print(f"🔄 Graphique utilise get_latest_market_data: {len(fresh_data)} points")
                        self.nano_container.create_dominance_donut_chart(fresh_data_reset)
                    else:
                        print("⚠️ Graphique fallback sur dom_evolution")
                        self.nano_container.create_dominance_donut_chart(dom_evolution)
                except Exception as e:
                    print(f"Erreur graphique fresh data: {e}")
                    self.nano_container.create_dominance_donut_chart(dom_evolution)
            elif hasattr(self.nano_container, 'create_dominance_chart'):
                try:
                    fresh_data = analyzer.db.get_latest_market_data(limit=50)
                    if not fresh_data.empty:
                        fresh_data_reset = fresh_data.reset_index()
                        self.nano_container.create_dominance_chart(fresh_data_reset)
                    else:
                        self.nano_container.create_dominance_chart(dom_evolution)
                except:
                    self.nano_container.create_dominance_chart(dom_evolution)
            elif hasattr(self.nano_container, 'plot_dominance_plotly'):
                self.nano_container.plot_dominance_plotly(dom_evolution)
                
        except Exception as e:
            print(f"Erreur dominance update: {e}")
            self.nano_container.set_banner_status("neutral", "🟡 BTC Dominance")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")
            # Essayer d'afficher un graphique vide
            if hasattr(self.nano_container, 'create_dominance_donut_chart'):
                import pandas as pd
                self.nano_container.create_dominance_donut_chart(pd.DataFrame())
    
    def _update_price(self, analyzer, symbol):
        """Met à jour un container de prix"""
        status, phrase = analyzer.analyze_price_trend(symbol)
        variations = analyzer.calculate_variations(symbol)

        symbol_short = symbol.split('/')[0]
        self.nano_container.set_banner_status(status, f"{symbol_short} Analysis")
        self.nano_container.set_phrase(phrase)
        self.nano_container.update_variations(variations)

        # Utiliser la méthode spécifique pour les prix
        if hasattr(self.nano_container, 'create_candlestick_chart'):
            try:
                ohlcv_data = analyzer.get_ohlcv_data(symbol, "1h", 100)
                self.nano_container.create_candlestick_chart(ohlcv_data, f"{symbol} Price Chart")
            except Exception as e:
                print(f"Erreur candlestick chart: {e}")
                import pandas as pd
                self.nano_container.create_candlestick_chart(pd.DataFrame(), f"{symbol} Price Chart")
        elif hasattr(self.nano_container, 'create_price_chart'):
            try:
                price_data = analyzer.get_price_evolution(symbol, 48)
                self.nano_container.create_price_chart(price_data, symbol_short, f"{symbol_short} Price Evolution")
            except Exception as e:
                print(f"Erreur price chart: {e}")
                import pandas as pd
                self.nano_container.create_price_chart(pd.DataFrame(), symbol_short, f"{symbol_short} Price Evolution")
        elif hasattr(self.nano_container, 'plot_candlestick_plotly'):
            try:
                ohlcv_data = analyzer.get_ohlcv_data(symbol, "1h", 100)
                self.nano_container.plot_candlestick_plotly(ohlcv_data, f"{symbol} Chart")
            except Exception as e:
                print(f"Erreur plotly chart: {e}")
    
    @audit_data_function
    def _update_volume(self, analyzer):
        """Met à jour le container de volume"""
        vol_data = analyzer.get_volume_data()
        status, phrase = analyzer.analyze_volume_trend()

        self.nano_container.set_banner_status(status, f"📊 Vol: ${vol_data['global']/1e9:.2f}B")
        self.nano_container.set_phrase(phrase)

        # Calculer les variations spécialisées pour le volume
        variations = analyzer.calculate_volume_variations()
        self.nano_container.update_variations(variations)

        # Utiliser la méthode spécifique pour volume
        if hasattr(self.nano_container, 'create_volume_chart'):
            try:
                volume_evolution = analyzer.get_volume_evolution(48)
                self.nano_container.create_volume_chart(volume_evolution)
            except Exception as e:
                print(f"Erreur volume chart: {e}")
                import pandas as pd
                self.nano_container.create_volume_chart(pd.DataFrame())
        elif hasattr(self.nano_container, 'plot_volume_plotly'):
            import pandas as pd
            volume_evolution = pd.DataFrame()
            self.nano_container.plot_volume_plotly(volume_evolution)
    
    def _update_marketcap(self, analyzer):
        """Met à jour le container de market cap avec informations claires"""
        try:
            mc_data = analyzer.get_marketcap_data()
            status, phrase = analyzer.analyze_capital_flows()

            # Afficher le Market Cap total dans le banner
            self.nano_container.set_banner_status(status, f"💰 MC: ${mc_data['global']/1e12:.2f}T")

            # Calculer les variations spécialisées pour le market cap
            variations = analyzer.calculate_marketcap_variations()
            self.nano_container.update_variations(variations)

            # CORRECTION: Phrase plus claire distinguant Market Cap total et flux
            # Récupérer les données d'évolution pour calculer les vraies variations
            mc_evolution = analyzer.get_marketcap_evolution(24)
            if not mc_evolution.empty and len(mc_evolution) >= 2:
                # Calculer la variation sur 24h
                variation_24h = (mc_evolution["mc_global"].iloc[0] - mc_evolution["mc_global"].iloc[-1]) / mc_evolution["mc_global"].iloc[-1] * 100

                # Répartition par secteur
                btc_mc = mc_data['btc'] / 1e12
                eth_mc = mc_data['eth'] / 1e12
                alt_mc = mc_data['alt'] / 1e12

                # Phrase détaillée et claire
                detailed_phrase = f"Market Cap Global: ${mc_data['global']/1e12:.2f}T ({variation_24h:+.1f}% 24h). Répartition: BTC ${btc_mc:.2f}T ({mc_data['btc']/mc_data['global']*100:.1f}%), ETH ${eth_mc:.2f}T ({mc_data['eth']/mc_data['global']*100:.1f}%), ALT ${alt_mc:.2f}T ({mc_data['alt']/mc_data['global']*100:.1f}%)"

                self.nano_container.set_phrase(detailed_phrase)
                print(f"🔄 Market Cap: Total ${mc_data['global']/1e12:.2f}T, Variation 24h: {variation_24h:+.1f}%")
            else:
                # Fallback avec phrase de base
                self.nano_container.set_phrase(phrase)

            # Utiliser la méthode spécifique pour market cap
            if hasattr(self.nano_container, 'create_marketcap_chart'):
                mc_evolution = analyzer.get_marketcap_evolution(48)
                self.nano_container.create_marketcap_chart(mc_evolution)
            elif hasattr(self.nano_container, 'plot_marketcap_plotly'):
                mc_evolution = analyzer.get_marketcap_evolution(48)
                self.nano_container.plot_marketcap_plotly(mc_evolution)
        except Exception as e:
            print(f"Erreur marketcap update: {e}")
            self.nano_container.set_banner_status("neutral", "💰 Market Cap")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")
            # Essayer d'afficher un graphique vide
            if hasattr(self.nano_container, 'create_marketcap_chart'):
                import pandas as pd
                self.nano_container.create_marketcap_chart(pd.DataFrame())
    
    def _update_altseason(self, analyzer):
        """Met à jour le container altseason avec gauge"""
        alt_index, alt_msg = analyzer.calculate_altseason_index()

        if alt_index:
            if alt_index > 120:
                status = "bullish"
            elif alt_index < 80:
                status = "bearish"
            else:
                status = "neutral"
            self.nano_container.set_banner_status(status, f"🌀 Altseason: {alt_index:.0f}")
        else:
            self.nano_container.set_banner_status("neutral", "🌀 Altseason Index")

        self.nano_container.set_phrase(alt_msg)

        # Calculer les variations spécialisées pour l'altseason
        variations = analyzer.calculate_altseason_variations()
        self.nano_container.update_variations(variations)

        # Utiliser le gauge d'altseason
        if hasattr(self.nano_container, 'create_altseason_gauge'):
            self.nano_container.create_altseason_gauge(alt_index, alt_msg)
    
    def _update_risk(self, analyzer):
        """Met à jour le container de risque avec gauge"""
        risk_index, risk_msg = analyzer.calculate_risk_index()

        if risk_index:
            if risk_index < 30:
                status = "bearish"
            elif risk_index > 70:
                status = "bullish"
            else:
                status = "neutral"
            self.nano_container.set_banner_status(status, f"⚠️ Risk: {risk_index:.0f}")
        else:
            self.nano_container.set_banner_status("neutral", "⚠️ Risk Index")

        self.nano_container.set_phrase(risk_msg)

        # Calculer les variations spécialisées pour le risk index
        variations = analyzer.calculate_risk_variations()
        self.nano_container.update_variations(variations)

        # Utiliser le gauge de risque
        if hasattr(self.nano_container, 'create_risk_gauge'):
            self.nano_container.create_risk_gauge(risk_index, risk_msg)
    
    def _update_correlation(self, analyzer):
        """Met à jour le container de corrélation"""
        self.nano_container.set_banner_status("neutral", "🔄 Correlation Matrix")
        self.nano_container.set_phrase("Matrice de corrélation entre cryptomonnaies")

        # Vérifier que c'est un MatplotlibChartContainer avec chart_generator
        if (hasattr(self.nano_container, 'chart_generator') and
            self.nano_container.chart_generator is not None):
            try:
                import pandas as pd
                corr_data = pd.DataFrame()
                self.nano_container.create_correlation_heatmap(corr_data)
                self.nano_container.set_phrase("Matrice de corrélation affichée dans le dashboard")
            except Exception as e:
                self.nano_container.set_phrase(f"Erreur génération heatmap: {str(e)}")
        else:
            # Container normal ou matplotlib non disponible
            self.nano_container.set_phrase("Matrice de corrélation (matplotlib requis pour graphique intégré)")
    
    def _update_generic(self, analyzer):
        """Met à jour un container générique avec données réelles"""
        self.nano_container.set_banner_status(
            self.container_config["status"],
            self.container_config["title"]
        )
        self.nano_container.set_phrase(f"Analyse {self.container_config['analysis']} - Données synchronisées")

        # UTILISER LES VRAIES VARIATIONS depuis get_latest_market_data
        try:
            # Récupérer les données fraîches pour calculer les vraies variations
            timeframes = {"1m": 20, "5m": 100, "15m": 300, "1h": 1200, "4h": 4800, "1d": 28800}
            variations = {}

            for tf, minutes in timeframes.items():
                try:
                    # Calculer le nombre d'entrées approximatif (1 entrée toutes les 3 minutes)
                    limit_entries = max(int(minutes / 3), 10)
                    fresh_data = analyzer.db.get_latest_market_data(limit=limit_entries)

                    if not fresh_data.empty and len(fresh_data) >= 2:
                        df = fresh_data.reset_index().sort_values('created_at')

                        # Utiliser btc_dom comme référence pour les variations génériques
                        current = float(df['btc_dom'].iloc[-1])
                        previous = float(df['btc_dom'].iloc[0])
                        variation = ((current - previous) / previous) * 100 if previous != 0 else 0
                        variations[tf] = round(variation, 2)
                    else:
                        variations[tf] = 0.0
                except Exception as e:
                    print(f"Erreur variation {tf}: {e}")
                    variations[tf] = 0.0

            self.nano_container.update_variations(variations)
            print(f"🔄 Container générique utilise vraies variations: {variations}")

        except Exception as e:
            print(f"Erreur variations génériques: {e}")
            # Fallback: variations nulles au lieu d'aléatoires
            null_variations = {"1m": 0.0, "5m": 0.0, "15m": 0.0, "1h": 0.0, "4h": 0.0, "1d": 0.0}
            self.nano_container.update_variations(null_variations)

    def _update_capital_flows(self, analyzer):
        """Met à jour le container de flux de capitaux"""
        try:
            # Analyser les flux de capitaux basés sur les données fraîches
            # Utiliser moins de points pour avoir des variations plus sensibles
            latest_data = analyzer.db.get_latest_market_data(limit=20)

            if not latest_data.empty and len(latest_data) >= 5:
                # get_latest_market_data() retourne created_at comme INDEX, pas comme colonne
                df = latest_data.reset_index()  # Convertit l'index created_at en colonne
                df = df.sort_values('created_at')  # Maintenant created_at est une colonne

                # Calculer les flux de capitaux sur une période plus courte (plus sensible)
                # Comparer les 5 derniers points avec les 5 premiers
                recent_btc = df['btc_mc'].iloc[-5:].mean()
                older_btc = df['btc_mc'].iloc[:5].mean()
                btc_flow = (recent_btc - older_btc) / 1e9  # En milliards

                recent_eth = df['eth_mc'].iloc[-5:].mean()
                older_eth = df['eth_mc'].iloc[:5].mean()
                eth_flow = (recent_eth - older_eth) / 1e9  # En milliards

                recent_alt = df['alt_mc'].iloc[-5:].mean()
                older_alt = df['alt_mc'].iloc[:5].mean()
                alt_flow = (recent_alt - older_alt) / 1e9  # En milliards

                recent_total = df['mc_global'].iloc[-5:].mean()
                older_total = df['mc_global'].iloc[:5].mean()
                total_flow = (recent_total - older_total) / 1e9  # En milliards

                # Déterminer la direction dominante des flux
                flows = {'BTC': btc_flow, 'ETH': eth_flow, 'ALT': alt_flow}
                max_flow = max(flows.values(), key=abs)
                max_sector = [k for k, v in flows.items() if v == max_flow][0]

                # Déterminer le statut avec seuils plus bas (plus sensible)
                if total_flow > 1:  # Plus de 1B d'entrées
                    status = "bullish"
                    flow_text = "Entrants"
                elif total_flow < -1:  # Plus de 1B de sorties
                    status = "bearish"
                    flow_text = "Sortants"
                else:
                    status = "neutral"
                    flow_text = "Équilibrés"

                self.nano_container.set_banner_status(status, f"💸 Flux: {flow_text}")
                self.nano_container.set_phrase(f"Flux capitaux: {flow_text} ({total_flow:+.1f}B$). Dominant: {max_sector} ({max_flow:+.1f}B$). BTC: {btc_flow:+.1f}B$, ETH: {eth_flow:+.1f}B$, ALT: {alt_flow:+.1f}B$")

                # Calculer les variations spécialisées pour les capital flows
                variations = analyzer.calculate_capital_flows_variations()
                self.nano_container.update_variations(variations)

                print(f"🔄 Capital Flows utilise données fraîches: Total {total_flow:+.1f}B$, Dominant {max_sector} {max_flow:+.1f}B$")
            else:
                # Fallback avec analyse basique
                cap_status, cap_phrase = analyzer.analyze_capital_flows()
                self.nano_container.set_banner_status(cap_status, "💸 Capital Flows")
                self.nano_container.set_phrase(cap_phrase)
                print("🔄 Capital Flows utilise analyze_capital_flows")

            # Créer le graphique de flux de capitaux
            if hasattr(self.nano_container, 'create_capital_flows_chart'):
                # Créer un DataFrame compatible avec le graphique
                if not latest_data.empty:
                    chart_data = latest_data.reset_index()  # Convertit l'index en colonne

                    # Créer des colonnes compatibles avec create_capital_flows_chart
                    # Le graphique attend: btc_flows, eth_flows, alt_flows, total_flows
                    if len(chart_data) >= 2:
                        # Calculer les flux comme variations de market cap
                        chart_data['btc_flows'] = chart_data['btc_mc'].diff().fillna(0) / 1e9  # En milliards
                        chart_data['eth_flows'] = chart_data['eth_mc'].diff().fillna(0) / 1e9  # En milliards
                        chart_data['alt_flows'] = chart_data['alt_mc'].diff().fillna(0) / 1e9  # En milliards
                        chart_data['total_flows'] = chart_data['mc_global'].diff().fillna(0) / 1e9  # En milliards
                    else:
                        # Pas assez de données pour calculer les variations
                        chart_data['btc_flows'] = 0
                        chart_data['eth_flows'] = 0
                        chart_data['alt_flows'] = 0
                        chart_data['total_flows'] = 0

                    self.nano_container.create_capital_flows_chart(chart_data)
                else:
                    # Données vides - le graphique utilisera ses données simulées
                    self.nano_container.create_capital_flows_chart(latest_data)

        except Exception as e:
            print(f"Erreur capital flows update: {e}")
            self.nano_container.set_banner_status("neutral", "💸 Capital Flows")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")

    def _update_orderbook(self, analyzer):
        """Met à jour le container de profondeur du carnet d'ordres"""
        try:
            # Récupérer les données d'orderbook réelles depuis la base
            orderbook_data = analyzer.get_orderbook_data("BTC/USDT")
            print("✅ get_orderbook_data disponible, utilisation des données")

            if orderbook_data and 'bids' in orderbook_data and 'asks' in orderbook_data:
                # Calculer la profondeur du carnet avec gestion d'erreur
                try:
                    # Vérifier que nous avons des données valides
                    if not orderbook_data['bids'] or not orderbook_data['asks']:
                        raise ValueError("Données orderbook vides")

                    # Convertir explicitement en float pour éviter les erreurs de dtype
                    total_bids = 0.0
                    for bid in orderbook_data['bids'][:10]:
                        try:
                            total_bids += float(str(bid[1]))  # Conversion explicite string -> float
                        except (ValueError, IndexError):
                            continue

                    total_asks = 0.0
                    for ask in orderbook_data['asks'][:10]:
                        try:
                            total_asks += float(str(ask[1]))  # Conversion explicite string -> float
                        except (ValueError, IndexError):
                            continue

                    # Calculer le spread
                    try:
                        best_bid = float(str(orderbook_data['bids'][0][0]))
                        best_ask = float(str(orderbook_data['asks'][0][0]))
                        spread = ((best_ask - best_bid) / best_ask) * 100 if best_ask > 0 else 0
                    except (ValueError, IndexError):
                        spread = 0.01

                except Exception as e:
                    print(f"Erreur calcul orderbook: {e}")
                    total_bids = total_asks = 1000.0  # Valeurs par défaut
                    spread = 0.01

                # Déterminer le statut basé sur la liquidité
                if total_bids + total_asks > 1000:  # Haute liquidité
                    status = "bullish"
                    depth_text = "Profonde"
                elif total_bids + total_asks > 500:  # Liquidité moyenne
                    status = "neutral"
                    depth_text = "Moyenne"
                else:  # Faible liquidité
                    status = "bearish"
                    depth_text = "Faible"

                self.nano_container.set_banner_status(status, f"📚 Depth: {depth_text}")
                self.nano_container.set_phrase(f"Profondeur BTC/USDT: {depth_text}. Bids: {total_bids:.1f} BTC, Asks: {total_asks:.1f} BTC, Spread: {spread:.3f}%")

                # Calculer les variations spécialisées pour l'orderbook
                variations = analyzer.calculate_orderbook_variations()
                self.nano_container.update_variations(variations)

                print(f"🔄 Orderbook Depth utilise données réelles: Bids {total_bids:.1f}, Asks {total_asks:.1f}, Spread {spread:.3f}%")
            else:
                # Fallback avec données simulées réalistes
                import random
                total_bids = random.uniform(800, 1200)
                total_asks = random.uniform(800, 1200)
                spread = random.uniform(0.01, 0.05)

                status = "neutral"
                self.nano_container.set_banner_status(status, "📚 Orderbook Depth")
                self.nano_container.set_phrase(f"Profondeur BTC/USDT (simulée): Bids: {total_bids:.1f} BTC, Asks: {total_asks:.1f} BTC, Spread: {spread:.3f}%")

                print(f"🔄 Orderbook Depth utilise données simulées: Bids {total_bids:.1f}, Asks {total_asks:.1f}")

            # Créer le graphique d'orderbook avec gestion d'erreur améliorée
            if hasattr(self.nano_container, 'create_orderbook_chart'):
                try:
                    # Créer des données d'orderbook simplifiées pour éviter l'erreur dtype
                    simple_orderbook = {
                        'bids': [[95000.0, 1.5], [94950.0, 2.1], [94900.0, 1.8]],  # Prix, Quantité
                        'asks': [[95050.0, 1.2], [95100.0, 1.9], [95150.0, 2.3]],
                        'symbol': 'BTC/USDT'
                    }
                    self.nano_container.create_orderbook_chart(simple_orderbook, "BTC/USDT")
                    print("✅ Graphique orderbook créé avec données simplifiées")
                except Exception as e:
                    print(f"⚠️ Erreur graphique orderbook: {e}")
                    # Créer un graphique de fallback simple
                    if hasattr(self.nano_container, 'chart_generator'):
                        try:
                            # Utiliser un graphique de volume comme fallback
                            latest_data = analyzer.db.get_latest_market_data(limit=20)
                            if not latest_data.empty:
                                chart_data = latest_data.reset_index()
                                self.nano_container.create_volume_chart(chart_data)
                                print("✅ Fallback: graphique volume utilisé pour orderbook")
                        except:
                            pass

        except Exception as e:
            print(f"Erreur orderbook update: {e}")
            self.nano_container.set_banner_status("neutral", "📚 Orderbook Depth")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")

    def _update_vol_dominance(self, analyzer):
        """Met à jour le container volume vs dominance"""
        try:
            # Récupérer les données fraîches pour volume et dominance
            latest_data = analyzer.db.get_latest_market_data(limit=100)  # 100 derniers points

            if not latest_data.empty and len(latest_data) >= 10:
                # Convertir les données pour le calcul de corrélation
                df = latest_data.reset_index()
                df = df.sort_values('created_at')

                # Calculer la corrélation réelle entre volume et dominance
                try:
                    correlation = df['vol_global'].corr(df['btc_dom'])
                    if pd.isna(correlation):
                        correlation = 0.0
                except Exception as e:
                    print(f"Erreur calcul corrélation: {e}")
                    correlation = 0.0

                # Récupérer les données actuelles pour l'affichage
                vol_data = analyzer.get_volume_data()
                dom_data = analyzer.get_dominance_data()

                if vol_data and dom_data:
                    # Déterminer le statut basé sur la corrélation
                    if abs(correlation) > 0.5:
                        status = "bullish" if correlation > 0 else "bearish"
                        corr_text = "Forte"
                    elif abs(correlation) > 0.3:
                        status = "neutral"
                        corr_text = "Modérée"
                    else:
                        status = "neutral"
                        corr_text = "Faible"

                    self.nano_container.set_banner_status(status, f"🔄 Vol/Dom: {correlation:.2f}")
                    self.nano_container.set_phrase(f"Corrélation Volume-Dominance: {corr_text} ({correlation:.2f}). Volume: ${vol_data['global']/1e9:.1f}B, BTC Dom: {dom_data['btc']:.2f}%")

                    # Calculer les variations spécialisées pour vol/dominance
                    variations = analyzer.calculate_vol_dominance_variations()
                    self.nano_container.update_variations(variations)

                    print(f"🔄 Vol vs Dominance: Corrélation {correlation:.2f}, Vol {vol_data['global']/1e9:.1f}B, Dom {dom_data['btc']:.2f}%")
                else:
                    self.nano_container.set_banner_status("neutral", "🔄 Vol vs Dominance")
                    self.nano_container.set_phrase("Données volume/dominance indisponibles")
            else:
                self.nano_container.set_banner_status("neutral", "🔄 Vol vs Dominance")
                self.nano_container.set_phrase("Données insuffisantes pour l'analyse volume/dominance")

            # Utiliser le scatter plot volume vs dominance
            if hasattr(self.nano_container, 'create_vol_dominance_scatter'):
                self.nano_container.create_vol_dominance_scatter(latest_data)

        except Exception as e:
            print(f"Erreur vol_dominance update: {e}")
            self.nano_container.set_banner_status("neutral", "🔄 Vol vs Dominance")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")

    def _update_correlation(self, analyzer):
        """Met à jour le container de matrice de corrélation"""
        try:
            # Calculer les corrélations basées sur les données fraîches
            latest_data = analyzer.db.get_latest_market_data(limit=100)

            if not latest_data.empty and len(latest_data) >= 20:
                # Calculer les corrélations entre BTC, ETH et ALT dominances
                # get_latest_market_data() retourne created_at comme INDEX, pas comme colonne
                df = latest_data.reset_index()  # Convertit l'index created_at en colonne
                df = df.sort_values('created_at')  # Maintenant created_at est une colonne

                # Créer une matrice de corrélation simple
                correlations = {
                    'BTC-ETH': df['btc_dom'].corr(df['eth_dom']),
                    'BTC-ALT': df['btc_dom'].corr(df['alt_dom']),
                    'ETH-ALT': df['eth_dom'].corr(df['alt_dom']),
                    'BTC-VOL': df['btc_dom'].corr(df['vol_global']),
                    'ETH-VOL': df['eth_dom'].corr(df['vol_global']),
                    'MC-VOL': df['mc_global'].corr(df['vol_global'])
                }

                # Trouver la corrélation la plus forte
                max_corr = max(correlations.values(), key=abs)
                max_pair = [k for k, v in correlations.items() if v == max_corr][0]

                # Déterminer le statut
                if abs(max_corr) > 0.7:
                    status = "bullish" if max_corr > 0 else "bearish"
                    corr_text = "Forte"
                elif abs(max_corr) > 0.4:
                    status = "neutral"
                    corr_text = "Modérée"
                else:
                    status = "neutral"
                    corr_text = "Faible"

                self.nano_container.set_banner_status(status, f"🔗 Corr: {corr_text}")
                self.nano_container.set_phrase(f"Corrélations crypto: {corr_text}. Plus forte: {max_pair} ({max_corr:.2f}). BTC-ETH: {correlations['BTC-ETH']:.2f}, BTC-ALT: {correlations['BTC-ALT']:.2f}")

                # Calculer les variations spécialisées pour la corrélation
                variations = analyzer.calculate_correlation_variations()
                self.nano_container.update_variations(variations)

                print(f"🔄 Correlation Matrix utilise données fraîches: {len(df)} points, Max corr: {max_pair} ({max_corr:.2f})")
            else:
                # Fallback avec corrélations typiques
                self.nano_container.set_banner_status("neutral", "🔗 Correlation Matrix")
                self.nano_container.set_phrase("Corrélations crypto typiques: BTC-ETH: 0.65, BTC-ALT: -0.45, ETH-ALT: 0.25. Diversifiez avec des actifs peu corrélés.")
                print("🔄 Correlation Matrix utilise données par défaut")

            # Créer une heatmap de corrélation SIMPLE (pas d'énorme tableau)
            if (hasattr(self.nano_container, 'chart_generator') and
                self.nano_container.chart_generator is not None):
                try:
                    # Créer un DataFrame de corrélation simple (6x6 max)
                    import pandas as pd
                    if not latest_data.empty:
                        df_simple = latest_data[['btc_dom', 'eth_dom', 'alt_dom', 'vol_global', 'mc_global']].copy()
                        df_simple.columns = ['BTC', 'ETH', 'ALT', 'VOL', 'MC']  # Noms courts
                        corr_matrix = df_simple.corr()  # Matrice 5x5 seulement
                        print(f"🔄 Correlation Matrix: Matrice {corr_matrix.shape[0]}x{corr_matrix.shape[1]} créée")
                        self.nano_container.create_correlation_heatmap(corr_matrix)
                    else:
                        # Matrice par défaut très simple
                        default_corr = pd.DataFrame({
                            'BTC': [1.0, -0.65, -0.45],
                            'ETH': [-0.65, 1.0, 0.25],
                            'ALT': [-0.45, 0.25, 1.0]
                        }, index=['BTC', 'ETH', 'ALT'])
                        self.nano_container.create_correlation_heatmap(default_corr)
                except Exception as e:
                    print(f"Erreur génération heatmap: {e}")
                    # Fallback: pas de graphique, juste le texte
                    pass

        except Exception as e:
            print(f"Erreur correlation update: {e}")
            self.nano_container.set_banner_status("neutral", "🔗 Correlation Matrix")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")

    def _update_forecast(self, analyzer):
        """Met à jour le container de prévision de prix"""
        try:
            # Récupérer les données OHLCV réelles pour BTC
            ohlcv_data = analyzer.get_ohlcv_data("BTC/USDT", "1h", 100)

            if not ohlcv_data.empty and len(ohlcv_data) >= 20:
                # Calculer des indicateurs techniques simples pour la prévision
                df = ohlcv_data.copy()
                df = df.sort_values('timestamp')

                # Calculer moyennes mobiles
                df['ma_20'] = df['close'].rolling(window=20).mean()
                df['ma_50'] = df['close'].rolling(window=50).mean() if len(df) >= 50 else df['close'].rolling(window=len(df)//2).mean()

                # Prix actuel et moyennes
                current_price = float(df['close'].iloc[-1])
                ma_20 = float(df['ma_20'].iloc[-1]) if not pd.isna(df['ma_20'].iloc[-1]) else current_price
                ma_50 = float(df['ma_50'].iloc[-1]) if not pd.isna(df['ma_50'].iloc[-1]) else current_price

                # Tendance basée sur les moyennes mobiles
                if current_price > ma_20 > ma_50:
                    status = "bullish"
                    trend_text = "Haussière"
                    forecast = "📈 Tendance haussière confirmée"
                elif current_price < ma_20 < ma_50:
                    status = "bearish"
                    trend_text = "Baissière"
                    forecast = "📉 Tendance baissière confirmée"
                else:
                    status = "neutral"
                    trend_text = "Neutre"
                    forecast = "⚖️ Consolidation en cours"

                # Calculer le support et la résistance approximatifs
                recent_high = df['high'].tail(20).max()
                recent_low = df['low'].tail(20).min()

                self.nano_container.set_banner_status(status, f"📈 Trend: {trend_text}")
                self.nano_container.set_phrase(f"Prévision BTC: {forecast}. Prix: ${current_price:,.0f}, MA20: ${ma_20:,.0f}, Support: ${recent_low:,.0f}, Résistance: ${recent_high:,.0f}")

                # Calculer les variations spécialisées pour les prévisions
                variations = analyzer.calculate_forecast_variations()
                self.nano_container.update_variations(variations)

                print(f"🔄 Price Forecast utilise données OHLCV réelles: {len(df)} points, Prix ${current_price:,.0f}, Trend {trend_text}")
            else:
                # Fallback avec analyse basée sur market cap
                latest_data = analyzer.db.get_latest_market_data(limit=50)
                if not latest_data.empty:
                    df = latest_data.reset_index().sort_values('created_at')
                    mc_trend = (df['mc_global'].iloc[-1] - df['mc_global'].iloc[0]) / df['mc_global'].iloc[0] * 100

                    if mc_trend > 2:
                        status = "bullish"
                        forecast_text = "Optimiste"
                    elif mc_trend < -2:
                        status = "bearish"
                        forecast_text = "Pessimiste"
                    else:
                        status = "neutral"
                        forecast_text = "Neutre"

                    self.nano_container.set_banner_status(status, f"📈 Forecast: {forecast_text}")
                    self.nano_container.set_phrase(f"Prévision basée sur Market Cap: {forecast_text} ({mc_trend:+.1f}%). Surveillez l'évolution du sentiment global.")
                    print(f"🔄 Price Forecast utilise Market Cap: Trend {mc_trend:+.1f}%")
                else:
                    self.nano_container.set_banner_status("neutral", "📈 Price Forecast")
                    self.nano_container.set_phrase("Prévision en cours de calcul...")

            # Créer un graphique de prévision
            if (hasattr(self.nano_container, 'chart_generator') and
                self.nano_container.chart_generator is not None):
                try:
                    if not ohlcv_data.empty:
                        self.nano_container.create_candlestick_chart(ohlcv_data, "BTC Price Forecast")
                    else:
                        # Utiliser les données de market cap comme fallback
                        latest_data = analyzer.db.get_latest_market_data(limit=50)
                        self.nano_container.create_marketcap_chart(latest_data)
                except Exception as e:
                    print(f"Erreur génération forecast chart: {e}")

        except Exception as e:
            print(f"Erreur forecast update: {e}")
            self.nano_container.set_banner_status("neutral", "📈 Price Forecast")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")

    def _update_sentiment(self, analyzer):
        """Met à jour le container de sentiment de marché"""
        try:
            # Calculer le sentiment basé sur les données fraîches
            latest_data = analyzer.db.get_latest_market_data(limit=100)

            if not latest_data.empty:
                # Calculer un sentiment basé sur la volatilité et les flux
                vol_data = analyzer.get_volume_data()
                dom_data = analyzer.get_dominance_data()

                # Sentiment simple basé sur dominance et volume
                if vol_data and dom_data:
                    # Plus de volume + dominance stable = sentiment positif
                    volume_score = min(100, vol_data['global'] / 1e11)  # Normaliser le volume
                    dominance_stability = 100 - abs(dom_data['btc'] - 55)  # Stabilité autour de 55%
                    sentiment_score = (volume_score + dominance_stability) / 2

                    if sentiment_score > 70:
                        status = "bullish"
                        sentiment_text = "Optimiste"
                    elif sentiment_score < 40:
                        status = "bearish"
                        sentiment_text = "Pessimiste"
                    else:
                        status = "neutral"
                        sentiment_text = "Neutre"

                    self.nano_container.set_banner_status(status, f"🎯 Sentiment: {sentiment_text}")
                    self.nano_container.set_phrase(f"Sentiment marché: {sentiment_text} ({sentiment_score:.0f}/100). Volume: ${vol_data['global']/1e9:.1f}B, BTC Dom: {dom_data['btc']:.2f}%")

                    # Calculer les variations spécialisées pour le sentiment
                    variations = analyzer.calculate_sentiment_variations()
                    self.nano_container.update_variations(variations)

                    print(f"🔄 Market Sentiment utilise données fraîches: Score {sentiment_score:.0f}, Vol {vol_data['global']/1e9:.1f}B")
                else:
                    self.nano_container.set_banner_status("neutral", "🎯 Market Sentiment")
                    self.nano_container.set_phrase("Calcul du sentiment en cours...")
            else:
                self.nano_container.set_banner_status("neutral", "🎯 Market Sentiment")
                self.nano_container.set_phrase("Données insuffisantes pour le sentiment")

            # Créer un graphique de volume pour représenter le sentiment
            if (hasattr(self.nano_container, 'chart_generator') and
                self.nano_container.chart_generator is not None):
                try:
                    # Convertir les données pour le graphique
                    if not latest_data.empty:
                        chart_data = latest_data.reset_index()  # Convertir l'index en colonne
                        # S'assurer que les colonnes nécessaires existent
                        if 'vol_global' in chart_data.columns and 'created_at' in chart_data.columns:
                            self.nano_container.create_volume_chart(chart_data)
                        else:
                            print(f"⚠️ Colonnes manquantes pour volume chart: {list(chart_data.columns)}")
                            self.nano_container.create_volume_chart(None)  # Utiliser données simulées
                    else:
                        self.nano_container.create_volume_chart(None)  # Utiliser données simulées
                except Exception as e:
                    print(f"Erreur création graphique sentiment: {e}")
                    self.nano_container.set_phrase(f"Erreur génération sentiment: {str(e)}")

        except Exception as e:
            print(f"Erreur sentiment update: {e}")
            self.nano_container.set_banner_status("neutral", "🎯 Market Sentiment")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")

    def _update_momentum(self, analyzer):
        """Met à jour le container de momentum de tendance"""
        try:
            # Calculer le momentum basé sur les données fraîches
            latest_data = analyzer.db.get_latest_market_data(limit=100)

            if not latest_data.empty and len(latest_data) >= 10:
                # Calculer le momentum sur les 10 derniers points
                # get_latest_market_data() retourne created_at comme INDEX, pas comme colonne
                df = latest_data.reset_index()  # Convertit l'index created_at en colonne
                df = df.sort_values('created_at')  # Maintenant created_at est une colonne

                # Momentum basé sur l'évolution du market cap global
                recent_mc = df['mc_global'].iloc[-5:].mean()  # 5 derniers points
                older_mc = df['mc_global'].iloc[:5].mean()    # 5 premiers points

                momentum_change = ((recent_mc - older_mc) / older_mc) * 100 if older_mc != 0 else 0

                if momentum_change > 2:
                    status = "bullish"
                    momentum_text = "Haussier"
                elif momentum_change < -2:
                    status = "bearish"
                    momentum_text = "Baissier"
                else:
                    status = "neutral"
                    momentum_text = "Neutre"

                self.nano_container.set_banner_status(status, f"🌊 Momentum: {momentum_text}")
                self.nano_container.set_phrase(f"Momentum tendance: {momentum_text} ({momentum_change:+.2f}%). Market Cap: ${recent_mc/1e12:.2f}T")

                # Calculer les variations spécialisées pour le momentum
                variations = analyzer.calculate_momentum_variations()
                self.nano_container.update_variations(variations)

                print(f"🔄 Trend Momentum utilise données fraîches: {momentum_change:+.2f}%, MC {recent_mc/1e12:.2f}T")
            else:
                self.nano_container.set_banner_status("neutral", "🌊 Trend Momentum")
                self.nano_container.set_phrase("Calcul du momentum en cours...")

            # Créer un graphique de market cap pour représenter le momentum
            if (hasattr(self.nano_container, 'chart_generator') and
                self.nano_container.chart_generator is not None):
                try:
                    # Convertir les données pour le graphique
                    if not latest_data.empty:
                        chart_data = latest_data.reset_index()  # Convertir l'index en colonne
                        # S'assurer que les colonnes nécessaires existent
                        if 'mc_global' in chart_data.columns and 'created_at' in chart_data.columns:
                            self.nano_container.create_marketcap_chart(chart_data)
                        else:
                            print(f"⚠️ Colonnes manquantes pour marketcap chart: {list(chart_data.columns)}")
                            self.nano_container.create_marketcap_chart(None)  # Utiliser données simulées
                    else:
                        self.nano_container.create_marketcap_chart(None)  # Utiliser données simulées
                except Exception as e:
                    print(f"Erreur création graphique momentum: {e}")
                    self.nano_container.set_phrase(f"Erreur génération momentum: {str(e)}")

        except Exception as e:
            print(f"Erreur momentum update: {e}")
            self.nano_container.set_banner_status("neutral", "🌊 Trend Momentum")
            self.nano_container.set_phrase(f"Erreur: {str(e)}")





    
    def _update_dominance_analysis(self, analyzer):
        """Met à jour l'analyse de dominance"""
        self._update_dominance(analyzer)
        
    def _update_altseason_analysis(self, analyzer):
        """Met à jour l'analyse de saisonnalité alternative"""
        self._update_altseason(analyzer)
        
    def _update_risk_analysis(self, analyzer):
        """Met à jour l'analyse de risque"""
        self._update_risk(analyzer)
        
    def _update_volume_analysis(self, analyzer):
        """Met à jour l'analyse de volume"""
        self._update_volume(analyzer)
        
    def _update_marketcap_analysis(self, analyzer):
        """Met à jour l'analyse de capitalisation boursière"""
        self._update_marketcap(analyzer)
        
    def _update_capital_flows_analysis(self, analyzer):
        """Met à jour l'analyse de flux de capitaux"""
        self._update_capital_flows(analyzer)
        
    def _update_orderbook_analysis(self, analyzer):
        """Met à jour l'analyse du carnet d'ordres"""
        self._update_orderbook(analyzer)
        
    def _update_vol_dominance_analysis(self, analyzer):
        """Met à jour l'analyse de dominance du volume"""
        self._update_vol_dominance(analyzer)
        
    def _update_correlation_analysis(self, analyzer):
        """Met à jour l'analyse de corrélation"""
        self._update_correlation(analyzer)
        
    def _update_forecast_analysis(self, analyzer):
        """Met à jour l'analyse de prévision"""
        self._update_forecast(analyzer)
        
    def _update_sentiment_analysis(self, analyzer):
        """Met à jour l'analyse de sentiment"""
        self._update_sentiment(analyzer)
        
    def _update_momentum_analysis(self, analyzer):
        """Met à jour l'analyse de momentum"""
        self._update_momentum(analyzer)










    def _update_ai_analysis(self, analyzer):
        """Met à jour le container d'analyse IA"""
        try:
            # Créer l'analyseur IA si pas encore fait
            if not hasattr(self, 'ai_analyzer'):
                self.ai_analyzer = NanoAIAnalyzer(analyzer.db, analyzer)

            # Lancer l'analyse IA
            report = self.ai_analyzer.run_analysis_cycle()

            if report:
                # Mettre à jour le banner selon le nombre de situations
                situations_count = len(report.get('situations', []))
                if situations_count > 0:
                    high_severity = [s for s in report['situations'] if s.get('severity') in ['high', 'critical']]
                    if high_severity:
                        status = "bearish" if any(s.get('type') == 'bearish' for s in high_severity) else "bullish"
                    else:
                        status = "neutral"
                else:
                    status = "neutral"

                self.nano_container.set_banner_status(status, f"🤖 AI: {situations_count} situations")

                # Afficher l'analyse IA
                ai_text = report.get('ai_analysis', 'Aucune analyse disponible')
                self.nano_container.set_phrase(ai_text)

            else:
                self.nano_container.set_banner_status("neutral", "🤖 AI Analysis")
                self.nano_container.set_phrase("Marché stable, aucune situation particulière détectée.")

        except Exception as e:
            print(f"Erreur analyse IA: {e}")
            self.nano_container.set_banner_status("neutral", "🤖 AI Analysis")
            self.nano_container.set_phrase(f"IA non disponible: {str(e)}")

    def _update_notifications_analysis(self, analyzer):
        """Met à jour le container de notifications"""
        try:
            # Récupérer le gestionnaire de notifications depuis le dashboard
            dashboard = self.parent()
            while dashboard and not hasattr(dashboard, 'get_notification_manager'):
                dashboard = dashboard.parent()
            
            if dashboard:
                notification_manager = dashboard.get_notification_manager()
                if notification_manager and hasattr(self.nano_container, 'notification_manager'):
                    # Mettre à jour le gestionnaire de notifications dans la page
                    self.nano_container.notification_manager = notification_manager
                    
                    # Actualiser l'affichage
                    if hasattr(self.nano_container, 'refresh_display'):
                        self.nano_container.refresh_display()
                        
        except Exception as e:
            print(f"Erreur mise à jour notifications: {e}")

# Configurations prédéfinies pour les 12 containers - NOUVELLES IDÉES du reference.py
CONTAINER_CONFIGS = [
    # Row 0
    {"title": "🟡 BTC Dominance", "type": "plotly", "analysis": "dominance", "status": "bullish"},
    {"title": "🌀 Altseason Index", "type": "plotly", "analysis": "altseason", "status": "neutral"},
    {"title": "⚠️ Global Risk Index", "type": "plotly", "analysis": "risk", "status": "bearish"},
    {"title": "📊 Global Volume", "type": "plotly", "analysis": "volume", "status": "bullish"},

    # Row 1
    {"title": "💰 Market Cap", "type": "plotly", "analysis": "marketcap", "status": "bullish"},
    {"title": "💸 Capital Flows", "type": "plotly", "analysis": "capital_flows", "status": "neutral"},
    {"title": "📚 Orderbook Depth", "type": "plotly", "analysis": "orderbook", "status": "neutral"},
    {"title": "🔄 Vol vs Dominance", "type": "plotly", "analysis": "vol_dominance", "status": "bullish"},

    # Row 2
    {"title": "🔄 Correlation Matrix", "type": "plotly", "analysis": "correlation", "status": "neutral"},
    {"title": "📈 Price Forecast", "type": "plotly", "analysis": "forecast", "status": "neutral"},
    {"title": "🎯 Market Sentiment", "type": "plotly", "analysis": "sentiment", "status": "neutral"},
    {"title": "🌊 Trend Momentum", "type": "plotly", "analysis": "momentum", "status": "bullish"},

    # Container IA (nouveau)
    {"title": "🤖 AI Analysis", "type": "plotly", "analysis": "ai_analysis", "status": "neutral"},
    
    # Container Notifications (nouveau)
    {"title": "📢 Notifications", "type": "notifications", "analysis": "notifications", "status": "neutral"}
]

# === MÉTHODES D'ANALYSE IA ===

def run_ai_analysis_for_widget(widget):
    """Lance une analyse IA pour un widget spécifique"""
    try:
        analyzer = MarketAnalyzer()
        ai_analyzer = NanoAIAnalyzer(analyzer.db, analyzer)

        # Lancer l'analyse IA
        report = ai_analyzer.run_analysis_cycle()

        if report and report.get('situations'):
            print(f"🤖 IA: {report['summary']} - {report['ai_analysis'][:100]}...")
            return report

        return None

    except Exception as e:
        print(f"Erreur analyse IA: {e}")
        return None

def get_ai_analysis_for_container_data(container_data):
    """Récupère une analyse IA pour des données de container"""
    try:
        analyzer = MarketAnalyzer()
        ai_analyzer = NanoAIAnalyzer(analyzer.db, analyzer)

        ai_analysis = ai_analyzer.analyze_container_phrases(container_data)
        return ai_analysis if ai_analysis else "Analyse IA non disponible"

    except Exception as e:
        print(f"Erreur analyse IA container: {e}")
        return f"Erreur IA: {str(e)}"

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>CFBundleIconFile</key>
	<string>linguist.icns</string>
	<key>CFBundleIdentifier</key>
	<string>org.qt-project.Linguist</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleExecutable</key>
	<string>Linguist</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleTypeIconFile</key>
			<string>linguist.icns</string>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>qph</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>Qt Linguist &apos;Phrase Book&apos;</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleTypeIconFile</key>
			<string>linguist.icns</string>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>ts</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>Qt Translation Source</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleTypeIconFile</key>
			<string>linguist.icns</string>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>po</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>GNU Gettext Localization File</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleTypeIconFile</key>
			<string>linguist.icns</string>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>xlf</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>XLIFF Localization File</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
		</dict>
	</array>
	<key>NSHumanReadableCopyright</key>
	<string>(C) 2017 The Qt Company Ltd</string>
</dict>
</plist>

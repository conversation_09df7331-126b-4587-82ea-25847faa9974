#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Système d'audit et de correction des fonctions d'affichage des données
- Détection automatique des erreurs dans les fonctions _update_
- Validation de la cohérence des données
- Correction automatique des problèmes courants
- Monitoring en temps réel des performances
"""

import inspect
import traceback
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
import pandas as pd
import numpy as np
from dataclasses import dataclass
import logging

@dataclass
class AuditResult:
    """Résultat d'audit d'une fonction"""
    function_name: str
    success: bool
    execution_time: float
    error_message: Optional[str] = None
    data_quality_score: float = 0.0
    recommendations: List[str] = None
    
    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []

class DataDisplayAuditor:
    """Auditeur pour les fonctions d'affichage des données"""
    
    def __init__(self):
        self.audit_history: Dict[str, List[AuditResult]] = {}
        self.performance_thresholds = {
            'execution_time_warning': 2.0,  # secondes
            'execution_time_critical': 5.0,
            'data_quality_minimum': 0.7,
            'error_rate_maximum': 0.1
        }
        self.setup_logging()
    
    def setup_logging(self):
        """Configure le logging pour l'audit"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('DataAuditor')
    
    def audit_function(self, func, analyzer, *args, **kwargs) -> AuditResult:
        """Audite une fonction d'affichage de données"""
        func_name = func.__name__
        start_time = time.time()
        
        try:
            # Exécuter la fonction
            result = func(analyzer, *args, **kwargs)
            execution_time = time.time() - start_time
            
            # Évaluer la qualité des données
            data_quality = self._evaluate_data_quality(analyzer, func_name)
            
            # Créer le résultat d'audit
            audit_result = AuditResult(
                function_name=func_name,
                success=True,
                execution_time=execution_time,
                data_quality_score=data_quality,
                recommendations=self._generate_recommendations(func_name, execution_time, data_quality)
            )
            
            self.logger.info(f"✅ {func_name}: {execution_time:.3f}s, qualité: {data_quality:.2f}")
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = str(e)
            
            audit_result = AuditResult(
                function_name=func_name,
                success=False,
                execution_time=execution_time,
                error_message=error_msg,
                recommendations=self._generate_error_recommendations(func_name, error_msg)
            )
            
            self.logger.error(f"❌ {func_name}: {error_msg}")
        
        # Stocker l'historique
        if func_name not in self.audit_history:
            self.audit_history[func_name] = []
        self.audit_history[func_name].append(audit_result)
        
        # Nettoyer l'historique (garder seulement les 100 derniers)
        if len(self.audit_history[func_name]) > 100:
            self.audit_history[func_name] = self.audit_history[func_name][-100:]
        
        return audit_result
    
    def _evaluate_data_quality(self, analyzer, func_name: str) -> float:
        """Évalue la qualité des données pour une fonction"""
        try:
            quality_score = 1.0
            
            # Vérifier la disponibilité de la base de données
            if not hasattr(analyzer, 'db') or analyzer.db is None:
                quality_score -= 0.3
            
            # Vérifier la fraîcheur des données
            if hasattr(analyzer, 'db') and analyzer.db:
                try:
                    latest_data = analyzer.db.get_latest_market_data(limit=1)
                    if latest_data.empty:
                        quality_score -= 0.4
                    else:
                        # Vérifier l'âge des données
                        latest_time = pd.to_datetime(latest_data.iloc[0]['created_at'])
                        age = datetime.now() - latest_time.replace(tzinfo=None)
                        if age > timedelta(hours=1):
                            quality_score -= 0.2
                        elif age > timedelta(minutes=30):
                            quality_score -= 0.1
                except Exception:
                    quality_score -= 0.3
            
            # Vérifications spécifiques par fonction
            if 'dominance' in func_name:
                quality_score *= self._check_dominance_data_quality(analyzer)
            elif 'volume' in func_name:
                quality_score *= self._check_volume_data_quality(analyzer)
            elif 'price' in func_name:
                quality_score *= self._check_price_data_quality(analyzer)
            
            return max(0.0, min(1.0, quality_score))
            
        except Exception as e:
            self.logger.warning(f"Erreur évaluation qualité pour {func_name}: {e}")
            return 0.5
    
    def _check_dominance_data_quality(self, analyzer) -> float:
        """Vérifie la qualité des données de dominance"""
        try:
            if hasattr(analyzer, 'get_dominance_data'):
                dom_data = analyzer.get_dominance_data()
                if dom_data and all(key in dom_data for key in ['btc', 'eth', 'alt']):
                    total = dom_data['btc'] + dom_data['eth'] + dom_data['alt']
                    if 95 <= total <= 105:  # Tolérance de 5%
                        return 1.0
                    else:
                        return 0.7
            return 0.5
        except:
            return 0.3
    
    def _check_volume_data_quality(self, analyzer) -> float:
        """Vérifie la qualité des données de volume"""
        try:
            if hasattr(analyzer, 'get_volume_data'):
                vol_data = analyzer.get_volume_data()
                if vol_data and 'global' in vol_data and vol_data['global'] > 0:
                    return 1.0
            return 0.5
        except:
            return 0.3
    
    def _check_price_data_quality(self, analyzer) -> float:
        """Vérifie la qualité des données de prix"""
        try:
            if hasattr(analyzer, 'get_ohlcv_data'):
                ohlcv = analyzer.get_ohlcv_data("BTC/USDT", "1h", 10)
                if not ohlcv.empty and len(ohlcv) >= 5:
                    return 1.0
            return 0.5
        except:
            return 0.3
    
    def _generate_recommendations(self, func_name: str, execution_time: float, data_quality: float) -> List[str]:
        """Génère des recommandations d'amélioration"""
        recommendations = []
        
        # Recommandations de performance
        if execution_time > self.performance_thresholds['execution_time_critical']:
            recommendations.append(f"⚠️ Temps d'exécution critique ({execution_time:.2f}s). Optimiser les requêtes DB.")
        elif execution_time > self.performance_thresholds['execution_time_warning']:
            recommendations.append(f"⚡ Temps d'exécution élevé ({execution_time:.2f}s). Considérer la mise en cache.")
        
        # Recommandations de qualité des données
        if data_quality < self.performance_thresholds['data_quality_minimum']:
            recommendations.append(f"📊 Qualité des données faible ({data_quality:.2f}). Vérifier les sources de données.")
        
        # Recommandations spécifiques par fonction
        if 'dominance' in func_name and data_quality < 0.8:
            recommendations.append("🔄 Vérifier que la somme BTC+ETH+ALT = 100%")
        
        if 'volume' in func_name and data_quality < 0.8:
            recommendations.append("📈 Vérifier la cohérence des données de volume")
        
        return recommendations
    
    def _generate_error_recommendations(self, func_name: str, error_msg: str) -> List[str]:
        """Génère des recommandations pour corriger les erreurs"""
        recommendations = []
        
        if "database" in error_msg.lower() or "db" in error_msg.lower():
            recommendations.append("🗄️ Vérifier la connexion à la base de données")
            recommendations.append("🔄 Redémarrer le service de base de données si nécessaire")
        
        if "keyerror" in error_msg.lower():
            recommendations.append("🔑 Vérifier la structure des données retournées")
            recommendations.append("📝 Ajouter une validation des clés avant utilisation")
        
        if "matplotlib" in error_msg.lower():
            recommendations.append("📊 Problème avec matplotlib - vérifier la configuration du backend")
            recommendations.append("🔄 Essayer de basculer vers Plotly")
        
        if "plotly" in error_msg.lower():
            recommendations.append("📈 Problème avec Plotly - vérifier l'installation")
            recommendations.append("🔄 Fallback vers matplotlib recommandé")
        
        return recommendations
    
    def get_function_health_report(self, func_name: str) -> Dict[str, Any]:
        """Génère un rapport de santé pour une fonction"""
        if func_name not in self.audit_history:
            return {"status": "no_data", "message": "Aucune donnée d'audit disponible"}
        
        history = self.audit_history[func_name]
        recent_history = history[-10:]  # 10 dernières exécutions
        
        success_rate = sum(1 for r in recent_history if r.success) / len(recent_history)
        avg_execution_time = np.mean([r.execution_time for r in recent_history])
        avg_data_quality = np.mean([r.data_quality_score for r in recent_history if r.success])
        
        # Déterminer le statut de santé
        if success_rate >= 0.9 and avg_data_quality >= 0.8 and avg_execution_time < 2.0:
            status = "healthy"
        elif success_rate >= 0.7 and avg_data_quality >= 0.6:
            status = "warning"
        else:
            status = "critical"
        
        return {
            "status": status,
            "success_rate": success_rate,
            "avg_execution_time": avg_execution_time,
            "avg_data_quality": avg_data_quality,
            "recent_errors": [r.error_message for r in recent_history if not r.success],
            "recommendations": list(set(sum([r.recommendations for r in recent_history], [])))
        }
    
    def get_global_health_report(self) -> Dict[str, Any]:
        """Génère un rapport de santé global"""
        all_functions = list(self.audit_history.keys())
        function_reports = {func: self.get_function_health_report(func) for func in all_functions}
        
        # Statistiques globales
        healthy_count = sum(1 for report in function_reports.values() if report.get("status") == "healthy")
        warning_count = sum(1 for report in function_reports.values() if report.get("status") == "warning")
        critical_count = sum(1 for report in function_reports.values() if report.get("status") == "critical")
        
        global_health = "healthy" if critical_count == 0 and warning_count <= len(all_functions) * 0.2 else \
                       "warning" if critical_count <= len(all_functions) * 0.1 else "critical"
        
        return {
            "global_status": global_health,
            "total_functions": len(all_functions),
            "healthy_functions": healthy_count,
            "warning_functions": warning_count,
            "critical_functions": critical_count,
            "function_details": function_reports,
            "timestamp": datetime.now().isoformat()
        }

# Instance globale de l'auditeur
global_auditor = DataDisplayAuditor()

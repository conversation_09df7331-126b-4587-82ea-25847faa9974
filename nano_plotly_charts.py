#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Module de génération de graphiques Plotly modernes et interactifs
- Design professionnel avec thèmes sombre/clair
- Graphiques interactifs (zoom, pan, hover)
- Performance optimisée pour les mises à jour en temps réel
- Responsive design
"""

import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.io as pio
from PySide6.QtWidgets import QWidget, QVBoxLayout, QSizePolicy, QLabel
from PySide6.QtCore import Qt, QTimer
from PySide6.QtWebEngineWidgets import QWebEngineView
import tempfile
import os
from datetime import datetime, timedelta
import logging

# Configuration Plotly
pio.templates.default = "plotly_dark"

class PlotlyChartGenerator:
    """Générateur de graphiques Plotly modernes et interactifs"""
    
    def __init__(self, dark_mode=True):
        self.dark_mode = dark_mode
        self.db = None
        self._setup_theme()
        self._setup_database()
        
    def _setup_theme(self):
        """Configure les thèmes visuels modernes"""
        if self.dark_mode:
            # Thème sombre moderne
            self.bg_color = 'rgba(46, 52, 64, 0.95)'
            self.paper_color = 'rgba(46, 52, 64, 0.95)'
            self.text_color = '#E5E9F0'
            self.grid_color = '#4C566A'
            self.accent_color = '#88C0D0'
            
            # Palette de couleurs nordiques
            self.color_palette = [
                '#88C0D0',  # Bleu glacier
                '#BF616A',  # Rouge aurora
                '#A3BE8C',  # Vert aurora
                '#EBCB8B',  # Jaune aurora
                '#B48EAD',  # Violet aurora
                '#D08770',  # Orange aurora
                '#5E81AC',  # Bleu polaire
                '#81A1C1'   # Bleu clair
            ]
        else:
            # Thème clair moderne
            self.bg_color = 'rgba(255, 255, 255, 0.95)'
            self.paper_color = 'rgba(255, 255, 255, 0.95)'
            self.text_color = '#2E3440'
            self.grid_color = '#E5E9F0'
            self.accent_color = '#5E81AC'
            
            self.color_palette = [
                '#5E81AC',  # Bleu
                '#BF616A',  # Rouge
                '#A3BE8C',  # Vert
                '#D08770',  # Orange
                '#B48EAD',  # Violet
                '#EBCB8B',  # Jaune
                '#88C0D0',  # Cyan
                '#81A1C1'   # Bleu clair
            ]
    
    def _setup_database(self):
        """Configure la connexion à la base de données"""
        try:
            from nano_database import MarketDatabase
            self.db = MarketDatabase()
        except Exception as e:
            logging.warning(f"Impossible de connecter à la base de données: {e}")
            self.db = None
    
    def get_base_layout(self, title="", height=400):
        """Retourne la configuration de base pour tous les graphiques"""
        return dict(
            title=dict(
                text=title,
                font=dict(size=16, color=self.text_color, family="Arial, sans-serif"),
                x=0.5,
                xanchor='center'
            ),
            paper_bgcolor=self.paper_color,
            plot_bgcolor=self.bg_color,
            font=dict(color=self.text_color, family="Arial, sans-serif"),
            height=height,
            margin=dict(l=40, r=40, t=50, b=40),
            showlegend=True,
            legend=dict(
                bgcolor='rgba(0,0,0,0)',
                bordercolor='rgba(0,0,0,0)',
                font=dict(color=self.text_color)
            ),
            xaxis=dict(
                gridcolor=self.grid_color,
                gridwidth=1,
                color=self.text_color,
                tickfont=dict(size=10)
            ),
            yaxis=dict(
                gridcolor=self.grid_color,
                gridwidth=1,
                color=self.text_color,
                tickfont=dict(size=10)
            ),
            hovermode='x unified'
        )
    
    def create_figure_widget(self, fig, height=400) -> QWidget:
        """Crée un widget QWebEngineView pour afficher un graphique Plotly"""
        try:
            # Créer le widget conteneur
            widget = QWidget()
            widget.setAttribute(Qt.WA_DeleteOnClose)
            widget.setMinimumSize(300, height)
            
            # Layout
            layout = QVBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(0)
            
            # Créer le QWebEngineView
            web_view = QWebEngineView()
            web_view.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            
            # Générer le HTML
            html_content = self._generate_html(fig)
            
            # Charger le contenu
            web_view.setHtml(html_content)
            
            # Ajouter au layout
            layout.addWidget(web_view)
            
            # Stocker les références
            widget._web_view = web_view
            widget._figure = fig
            widget._is_valid = True
            
            return widget
            
        except Exception as e:
            print(f"❌ Erreur création widget Plotly: {e}")
            return self._create_error_widget(f"Erreur Plotly: {str(e)}")
    
    def _generate_html(self, fig):
        """Génère le HTML pour afficher le graphique Plotly"""
        # Configuration responsive
        config = {
            'displayModeBar': True,
            'displaylogo': False,
            'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d'],
            'responsive': True
        }
        
        # Convertir en HTML
        html = fig.to_html(
            include_plotlyjs='cdn',
            config=config,
            div_id="plotly-chart"
        )
        
        # Ajouter du CSS personnalisé pour l'intégration
        custom_css = """
        <style>
            body { 
                margin: 0; 
                padding: 0; 
                background: transparent;
                font-family: Arial, sans-serif;
            }
            #plotly-chart { 
                width: 100%; 
                height: 100vh; 
                background: transparent;
            }
            .modebar {
                opacity: 0.7;
                transition: opacity 0.3s;
            }
            .modebar:hover {
                opacity: 1;
            }
        </style>
        """
        
        # Injecter le CSS
        html = html.replace('<head>', f'<head>{custom_css}')
        
        return html
    
    def _create_error_widget(self, error_message: str) -> QWidget:
        """Crée un widget d'erreur informatif"""
        error_widget = QWidget()
        error_widget.setAttribute(Qt.WA_DeleteOnClose)
        error_widget.setMinimumSize(200, 100)
        
        layout = QVBoxLayout(error_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Style selon le thème
        bg_color = "#2E3440" if self.dark_mode else "#FFFFFF"
        text_color = "#D8DEE9" if self.dark_mode else "#2E3440"
        border_color = "#BF616A" if self.dark_mode else "#E74C3C"
        
        error_label = QLabel(f"📊 Graphique indisponible\n\n{error_message}")
        error_label.setWordWrap(True)
        error_label.setAlignment(Qt.AlignCenter)
        error_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color};
                background-color: {bg_color};
                border: 2px solid {border_color};
                border-radius: 8px;
                padding: 15px;
                font-size: 12px;
                font-family: 'Arial', sans-serif;
            }}
        """)
        
        layout.addWidget(error_label)
        return error_widget
    
    def update_dark_mode(self, dark_mode: bool):
        """Met à jour le thème"""
        self.dark_mode = dark_mode
        self._setup_theme()

    # ==================== GRAPHIQUES SPÉCIALISÉS ====================

    def create_dominance_donut_chart(self, dominance_data: pd.DataFrame = None) -> QWidget:
        """Crée un graphique donut moderne pour la dominance"""
        try:
            # Récupérer les données
            if dominance_data is None or dominance_data.empty:
                # Données par défaut
                btc_dom, eth_dom, alt_dom = 56.2, 18.3, 25.5
            else:
                latest = dominance_data.iloc[0]
                btc_dom = latest['btc_dom']
                eth_dom = latest['eth_dom']
                alt_dom = latest['alt_dom']

            # Créer le graphique donut
            fig = go.Figure(data=[go.Pie(
                labels=['BTC', 'ETH', 'ALT'],
                values=[btc_dom, eth_dom, alt_dom],
                hole=0.4,
                marker=dict(
                    colors=self.color_palette[:3],
                    line=dict(color='rgba(0,0,0,0.2)', width=2)
                ),
                textinfo='label+percent',
                textposition='outside',
                textfont=dict(size=12, color=self.text_color),
                hovertemplate='<b>%{label}</b><br>Dominance: %{value:.2f}%<extra></extra>'
            )])

            # Configuration du layout
            layout = self.get_base_layout("Market Dominance", height=350)
            layout.update(
                annotations=[dict(
                    text=f'Total<br>{btc_dom + eth_dom + alt_dom:.1f}%',
                    x=0.5, y=0.5,
                    font_size=14,
                    font_color=self.text_color,
                    showarrow=False
                )]
            )

            fig.update_layout(layout)
            return self.create_figure_widget(fig, 350)

        except Exception as e:
            print(f"❌ Erreur donut chart: {e}")
            return self._create_error_widget(f"Erreur donut: {str(e)}")

    def create_volume_chart(self, volume_data: pd.DataFrame = None) -> QWidget:
        """Crée un graphique en barres moderne pour le volume"""
        try:
            # Préparer les données
            if volume_data is None or volume_data.empty:
                # Données simulées
                dates = pd.date_range(end=pd.Timestamp.now(), periods=24, freq='h')
                volumes = np.random.lognormal(10, 0.3, size=len(dates)) * 1e9
                df = pd.DataFrame({'time': dates, 'volume': volumes})
            else:
                df = volume_data.copy()
                if 'created_at' in df.columns:
                    df['time'] = pd.to_datetime(df['created_at'])
                if 'vol_global' in df.columns:
                    df['volume'] = df['vol_global']
                elif 'volume' not in df.columns:
                    df['volume'] = np.random.lognormal(10, 0.3, size=len(df)) * 1e9

            # Trier et limiter aux dernières 24h
            df = df.sort_values('time').tail(24)

            # Créer le graphique
            fig = go.Figure()

            fig.add_trace(go.Bar(
                x=df['time'],
                y=df['volume'],
                name='Volume',
                marker=dict(
                    color=self.color_palette[0],
                    opacity=0.8,
                    line=dict(color=self.color_palette[0], width=1)
                ),
                hovertemplate='<b>Volume</b><br>Temps: %{x}<br>Volume: $%{y:.2s}<extra></extra>'
            ))

            # Configuration du layout
            layout = self.get_base_layout("Volume d'échange 24h", height=300)
            layout['yaxis'].update(
                title='Volume (USD)',
                tickformat='.2s'
            )
            layout['xaxis'].update(
                title='Heure',
                tickformat='%H:%M'
            )

            fig.update_layout(layout)
            return self.create_figure_widget(fig, 300)

        except Exception as e:
            print(f"❌ Erreur volume chart: {e}")
            return self._create_error_widget(f"Erreur volume: {str(e)}")

    def create_dominance_chart(self, dominance_data: pd.DataFrame = None, hours: int = 48) -> QWidget:
        """Crée un graphique linéaire pour l'évolution de la dominance"""
        try:
            # Préparer les données
            if dominance_data is None or dominance_data.empty:
                # Données simulées
                dates = pd.date_range(end=pd.Timestamp.now(), periods=hours, freq='h')
                btc_dom = 56 + np.cumsum(np.random.normal(0, 0.5, hours))
                eth_dom = 18 + np.cumsum(np.random.normal(0, 0.3, hours))
                alt_dom = 100 - btc_dom - eth_dom
                df = pd.DataFrame({
                    'time': dates,
                    'btc_dom': btc_dom,
                    'eth_dom': eth_dom,
                    'alt_dom': alt_dom
                })
            else:
                df = dominance_data.copy()
                if 'created_at' in df.columns:
                    df['time'] = pd.to_datetime(df['created_at'])

            # Trier et limiter
            df = df.sort_values('time').tail(hours)

            # Créer le graphique
            fig = go.Figure()

            # Ajouter les traces
            assets = ['btc_dom', 'eth_dom', 'alt_dom']
            names = ['BTC', 'ETH', 'ALT']

            for i, (asset, name) in enumerate(zip(assets, names)):
                if asset in df.columns:
                    fig.add_trace(go.Scatter(
                        x=df['time'],
                        y=df[asset],
                        mode='lines+markers',
                        name=name,
                        line=dict(
                            color=self.color_palette[i],
                            width=2.5
                        ),
                        marker=dict(size=4),
                        fill='tonexty' if i > 0 else None,
                        fillcolor=f'rgba{tuple(list(px.colors.hex_to_rgb(self.color_palette[i])) + [0.1])}',
                        hovertemplate=f'<b>{name}</b><br>Temps: %{{x}}<br>Dominance: %{{y:.2f}}%<extra></extra>'
                    ))

            # Configuration du layout
            layout = self.get_base_layout(f"Market Dominance ({hours}h)", height=320)
            layout['yaxis'].update(
                title='Dominance (%)',
                tickformat='.1f'
            )
            layout['xaxis'].update(
                title='Temps',
                tickformat='%H:%M'
            )

            fig.update_layout(layout)
            return self.create_figure_widget(fig, 320)

        except Exception as e:
            print(f"❌ Erreur dominance chart: {e}")
            return self._create_error_widget(f"Erreur dominance: {str(e)}")

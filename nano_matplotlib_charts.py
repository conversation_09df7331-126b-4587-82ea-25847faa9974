#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Module de génération de graphiques matplotlib modernes et attractifs
- Design de haute qualité avec background transparent
- Variété de types de graphiques (Bar, Bubble, Doughnut, Line, Mixed, Polar, Radar, Scatter)
- Tailles de police optimisées pour l'organisation
- Dimensions respectant le widget parent
"""

import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt
from PySide6 import QtCore  # Pour la vérification des objets C++
import os
import sys
import warnings

# Configuration sécurisée du backend matplotlib
def setup_matplotlib_backend():
    """Configure matplotlib de manière sécurisée"""
    try:
        # Supprimer les avertissements matplotlib
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

        # Configuration des variables d'environnement
        os.environ['QT_API'] = 'pyside6'

        # Détecter le backend approprié selon la plateforme
        if sys.platform == "darwin":  # macOS
            backend = 'Qt5Agg'
        elif sys.platform.startswith("linux"):  # Linux
            backend = 'Qt5Agg'
        else:  # Windows
            backend = 'Qt5Agg'

        # Configurer le backend seulement s'il n'est pas déjà défini
        current_backend = mpl.get_backend()
        if current_backend != backend:
            mpl.use(backend, force=False)

        print(f"✅ Backend Matplotlib configuré: {mpl.get_backend()}")
        return True

    except Exception as e:
        print(f"⚠️ Erreur configuration matplotlib: {e}")
        # Fallback vers le backend par défaut
        try:
            mpl.use('Agg', force=True)
            print(f"🔄 Fallback vers backend Agg: {mpl.get_backend()}")
            return False
        except:
            print("❌ Impossible de configurer matplotlib")
            return False

# Initialiser matplotlib
MATPLOTLIB_BACKEND_OK = setup_matplotlib_backend()

import matplotlib.dates as mdates
import seaborn as sns
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PySide6.QtWidgets import QWidget, QVBoxLayout, QSizePolicy, QMessageBox, QLabel
from PySide6.QtCore import Qt, QTimer
import logging
import traceback
from dateutil import tz
from datetime import datetime

# Configuration du logger
logger = logging.getLogger(__name__)

# ==================== CONSTANTES SYSTÈME TEMPORELLES ====================
# FORMAT UNIFORME POUR TOUS LES GRAPHIQUES - NE PAS MODIFIER !
SYSTEM_TIME_FORMAT = '%H:%M'  # Format hh:mm uniforme
SYSTEM_DATE_FORMAT = '%Y-%m-%d'  # Format date uniforme
SYSTEM_DATETIME_FORMAT = '%Y-%m-%d %H:%M'  # Format datetime complet
MAX_TIME_TICKS = 6  # Nombre maximum de ticks temporels
TIME_TICK_ROTATION = 45  # Rotation des labels temporels
SYSTEM_TZ = tz.tzlocal() 

print(f"🕐 SYSTÈME TEMPOREL INITIALISÉ: Format '{SYSTEM_TIME_FORMAT}', Max ticks: {MAX_TIME_TICKS}")
# ========================================================================

class MatplotlibChartGenerator:
    """Générateur de graphiques matplotlib modernes et attractifs"""
    
    def __init__(self, dark_mode=True):
        self.dark_mode = dark_mode
        self.db = None
        self._setup_theme()
        self._setup_database()
        # Dictionnaire pour suivre les figures actives
        self._active_figures = {}
        
    def _safe_draw(self, canvas):
        """Effectue un rendu sécurisé du canvas avec gestion d'erreurs améliorée"""
        if not canvas:
            return False

        try:
            # Vérifier que le canvas est toujours valide
            if not hasattr(canvas, 'figure') or canvas.figure is None:
                return False

            # Vérifier que le widget parent existe encore
            if hasattr(canvas, 'parent') and canvas.parent() is None:
                return False

            # Effectuer le rendu de manière asynchrone
            if canvas.isVisible():
                QTimer.singleShot(0, lambda: self._delayed_draw(canvas))
                return True
            else:
                return False

        except RuntimeError as e:
            if 'C++ object' in str(e) and 'deleted' in str(e):
                print("⚠️ Canvas déjà détruit, rendu ignoré")
            else:
                print(f"⚠️ Erreur RuntimeError lors du rendu: {e}")
            return False
        except Exception as e:
            print(f"⚠️ Erreur inattendue lors du rendu: {e}")
            return False

    def _delayed_draw(self, canvas):
        """Effectue le rendu avec un délai pour éviter les conflits"""
        try:
            if canvas and hasattr(canvas, 'draw_idle'):
                canvas.draw_idle()
        except Exception as e:
            print(f"⚠️ Erreur rendu différé: {e}")
    
    def _cleanup_figure_widget(self, widget):
        """
        Nettoie les ressources d'un widget de figure.
        Gère le cas où l'objet C++ a déjà été supprimé.
        """
        try:
            # Vérifier si le widget est toujours valide
            if widget is None:
                return
                
            # Vérifier si l'objet C++ a déjà été supprimé
            try:
                # Vérifier si le widget est toujours valide en accédant à une propriété
                if not widget.isWidgetType():
                    return
            except RuntimeError:
                # Si le C++ sous-jacent a été supprimé, une RuntimeError sera levée
                return
            except Exception:
                # Pour toute autre exception, on continue le nettoyage
                pass
                
            # Nettoyer le canvas s'il existe
            if hasattr(widget, '_canvas') and widget._canvas is not None:
                try:
                    # Désactiver les mises à jour pour éviter les appels pendant la suppression
                    widget._canvas.setUpdatesEnabled(False)
                    widget._canvas.setParent(None)
                    widget._canvas.deleteLater()
                    widget._canvas = None
                except RuntimeError as e:
                    if 'C++ object' not in str(e) or 'deleted' not in str(e):
                        print(f"Erreur lors de la fermeture du canvas: {e}")
                except Exception as e:
                    print(f"Erreur lors de la fermeture du canvas: {e}")
            
            # Nettoyer la figure matplotlib
            if hasattr(widget, '_figure') and widget._figure is not None:
                try:
                    import matplotlib.pyplot as plt
                    plt.close(widget._figure)
                    widget._figure = None
                except Exception as e:
                    if 'Figure' not in str(e) or 'deleted' not in str(e):
                        print(f"Erreur lors de la fermeture de la figure: {e}")
            
            # Supprimer le widget parent s'il existe
            parent = widget.parent()
            if parent is not None:
                try:
                    parent.layout().removeWidget(widget)
                except Exception as e:
                    pass  # Le layout peut déjà avoir été nettoyé
            
            # Marquer le widget pour suppression
            try:
                widget.setParent(None)
                widget.deleteLater()
            except RuntimeError:
                pass  # L'objet a déjà été supprimé
            
        except Exception as e:
            if 'C++ object' not in str(e) or 'deleted' not in str(e):
                print(f"Erreur lors du nettoyage du widget: {e}")


    def to_system_tz(self, s):
        """
        Convertit une série de timestamps en fuseau HORAIRE système, en partant du principe que
        la base stocke du UTC ou du naive UTC.
        """
        ts = pd.to_datetime(s, utc=True, errors='coerce')
        return ts.dt.tz_convert(SYSTEM_TZ)


    
    def _setup_theme(self):
        """Configure le thème visuel moderne avec Seaborn"""
        try:
            # Configuration de base
            plt.style.use('seaborn-v0_8' if 'seaborn-v0_8' in plt.style.available else 'seaborn')
            mpl.rcParams['figure.facecolor'] = 'none'
            mpl.rcParams['axes.facecolor'] = 'none'
            mpl.rcParams['savefig.facecolor'] = 'none'
            mpl.rcParams['axes.edgecolor'] = 'none'
            mpl.rcParams['axes.linewidth'] = 0.0
            mpl.rcParams['font.family'] = 'sans-serif'
            mpl.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']
            mpl.rcParams['figure.autolayout'] = True
            
            # Vérification du backend
            print(f"Backend Matplotlib: {mpl.get_backend()}")
            
        except Exception as e:
            error_msg = f"Erreur lors de la configuration du thème: {str(e)}\n\n{traceback.format_exc()}"
            print(error_msg)
            QMessageBox.critical(None, "Erreur Matplotlib", f"Impossible de configurer Matplotlib:\n{str(e)}")
        
        if self.dark_mode:
            # Thème sombre moderne
            sns.set_theme(style='darkgrid')
            self.bg_color = 'transparent'
            self.text_color = '#E8E8E8'
            self.grid_color = '#404040'
            self.accent_color = '#00D4FF'
            
            # Palette de couleurs modernes
            self.palette = sns.color_palette([
                '#00D4FF',  # Cyan électrique
                '#FF6B6B',  # Rouge corail
                '#4ECDC4',  # Turquoise
                '#45B7D1',  # Bleu ciel
                '#96CEB4',  # Vert menthe
                '#FFEEAD'   # Jaune pâle
            ])
            
            # Définition des couleurs pour les lignes
            self.line_colors = [
                '#00D4FF',  # Cyan électrique
                '#FF6B6B',  # Rouge corail
                '#4ECDC4',  # Turquoise
                '#45B7D1',  # Bleu ciel
                '#96CEB4',  # Vert menthe
                '#FFEEAD',  # Jaune pâle
                '#FFEAA7',  # Jaune doux
                '#DDA0DD',  # Violet clair
                '#98D8C8'   # Vert aqua
            ]
        else:
            # Thème clair moderne
            sns.set_theme(style='whitegrid')
            self.bg_color = 'transparent'
            self.text_color = '#2C3E50'
            self.grid_color = '#BDC3C7'
            self.accent_color = '#3498DB'
            
            self.palette = sns.color_palette([
                '#3498DB',  # Bleu
                '#E74C3C',  # Rouge
                '#2ECC71',  # Vert
                '#F39C12',  # Orange
                '#9B59B6',  # Violet
                '#1ABC9C',  # Turquoise
                '#E67E22',  # Orange foncé
                '#34495E'   # Gris foncé
            ])
            
            # Définition des couleurs pour les lignes (thème clair)
            self.line_colors = [
                '#3498DB',  # Bleu
                '#E74C3C',  # Rouge
                '#2ECC71',  # Vert
                '#F39C12',  # Orange
                '#9B59B6',  # Violet
                '#1ABC9C',  # Turquoise
                '#27AE60',  # Vert émeraude
                '#8E44AD',  # Violet foncé
                '#16A085'   # Vert océan
            ]
            
        # Appliquer la palette
        sns.set_palette(self.palette)
        
        # Configuration des polices
        plt.rc('font', size=8)          # Taille de police par défaut
        plt.rc('axes', titlesize=9)     # Taille du titre des axes
        plt.rc('axes', labelsize=8)     # Taille des labels des axes
        plt.rc('xtick', labelsize=7)    # Taille des ticks X
        plt.rc('ytick', labelsize=7)    # Taille des ticks Y
        plt.rc('legend', fontsize=7)    # Taille de la légende
        plt.rc('figure', titlesize=10)  # Taille du titre de la figure
    
    def _setup_database(self):
        """Configure la connexion à la base de données"""
        try:
            from nano_database import MarketDatabase
            self.db = MarketDatabase()
        except Exception as e:
            logger.warning(f"Impossible de connecter à la base de données: {e}")
            self.db = None
    
    def create_figure_widget(self, figsize=(4, 3), dpi=100) -> QWidget:
        """Crée un widget matplotlib avec dimensions optimisées et gestion d'erreurs robuste"""

        # Vérifier que matplotlib est disponible
        if not MATPLOTLIB_BACKEND_OK:
            return self._create_error_widget("Matplotlib non disponible")

        try:
            print(f"📊 Création widget figure: {figsize}, DPI: {dpi}")

            # Création du widget conteneur avec gestion d'erreurs
            widget = QWidget()
            widget.setAttribute(Qt.WA_DeleteOnClose)
            widget.setMinimumSize(200, 150)  # Taille minimale pour éviter les erreurs

            try:
                # Configuration sécurisée de la figure
                fig = Figure(figsize=figsize, dpi=dpi, facecolor='none', edgecolor='none')
                fig.patch.set_alpha(0.0)

                # Configuration pour éviter les warnings
                fig.set_tight_layout(True)

                # Création du canvas avec vérifications
                if not hasattr(FigureCanvas, '__init__'):
                    raise ImportError("FigureCanvas non disponible")

                canvas = FigureCanvas(fig)
                canvas.setAttribute(Qt.WA_DeleteOnClose)

                # Configuration du layout
                layout = QVBoxLayout(widget)
                layout.setContentsMargins(2, 2, 2, 2)
                layout.setSpacing(0)
                layout.addWidget(canvas)

                # Politiques de taille optimisées
                widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
                canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

                # Stockage sécurisé des références
                widget._figure = fig
                widget._canvas = canvas
                widget._is_valid = True

                # Connexion du nettoyage
                widget.destroyed.connect(lambda: self._cleanup_figure_widget(widget))

                # Rendu initial sécurisé
                success = self._safe_draw(canvas)
                if success:
                    print("✅ Widget figure créé avec succès")
                else:
                    print("⚠️ Widget créé mais rendu initial échoué")

                return widget
                    
            except Exception as canvas_err:
                print(f"❌ Erreur création canvas: {canvas_err}")
                # Nettoyage de la figure
                try:
                    plt.close(fig)
                except:
                    pass
                return self._create_error_widget(f"Erreur canvas: {str(canvas_err)}")

            except Exception as fig_err:
                print(f"❌ Erreur création figure: {fig_err}")
                return self._create_error_widget(f"Erreur figure: {str(fig_err)}")

        except Exception as e:
            print(f"❌ Erreur critique widget: {e}")
            return self._create_error_widget(f"Erreur critique: {str(e)}")

    def _create_error_widget(self, error_message: str) -> QWidget:
        """Crée un widget d'erreur informatif"""
        error_widget = QWidget()
        error_widget.setAttribute(Qt.WA_DeleteOnClose)
        error_widget.setMinimumSize(200, 100)

        layout = QVBoxLayout(error_widget)
        layout.setContentsMargins(10, 10, 10, 10)

        # Style selon le thème
        bg_color = "#2E3440" if self.dark_mode else "#FFFFFF"
        text_color = "#D8DEE9" if self.dark_mode else "#2E3440"
        border_color = "#BF616A" if self.dark_mode else "#E74C3C"

        error_label = QLabel(f"📊 Graphique indisponible\n\n{error_message}")
        error_label.setWordWrap(True)
        error_label.setAlignment(Qt.AlignCenter)
        error_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color};
                background-color: {bg_color};
                border: 2px solid {border_color};
                border-radius: 8px;
                padding: 15px;
                font-size: 12px;
                font-family: 'Arial', sans-serif;
            }}
        """)

        layout.addWidget(error_label)
        return error_widget
                
            except Exception as fig_err:
                print(f"\n" + "!"*60)
                print(f"ERREUR CRITIQUE: Impossible de créer la figure")
                print(f"Type: {type(fig_err).__name__}")
                print(f"Message: {str(fig_err)}")
                print(f"Traceback: {traceback.format_exc()}")
                print("!"*60 + "\n")
                
                # Nettoyage des ressources en cas d'erreur
                if 'fig' in locals():
                    try:
                        import matplotlib.pyplot as plt
                        plt.close(fig)
                    except:
                        pass
                
                # Propagation de l'erreur pour une gestion plus haut niveau
                raise
                
        except Exception as e:
            print(f"\n" + "#"*70)
            print(f"ERREUR CRITIQUE DANS CREATE_FIGURE_WIDGET")
            print(f"Type: {type(e).__name__}")
            print(f"Message: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            print("#"*70 + "\n")
            
            # Nettoyage des ressources en cas d'erreur
            if 'fig' in locals():
                try:
                    import matplotlib.pyplot as plt
                    plt.close(fig)
                except:
                    pass
            
            # Créer un widget d'erreur de dernier recours
            error_widget = QWidget()
            error_widget.setAttribute(Qt.WA_DeleteOnClose)
            layout = QVBoxLayout(error_widget)
            
            error_label = QLabel(
                "ERREUR CRITIQUE\n\n"
                f"Impossible de créer le widget de figure\n"
                f"Erreur: {str(e)}"
            )
            error_label.setWordWrap(True)
            error_label.setStyleSheet(
                "color: white; "
                "padding: 15px; "
                "font-weight: bold; "
                "background-color: #d32f2f; " 
                "border: 2px solid #b71c1c; "
                "border-radius: 6px;"
            )
            layout.addWidget(error_label)
            
            # Nettoyage lors de la fermeture
            error_widget.destroyed.connect(lambda: self._cleanup_figure_widget(error_widget))
            
            return error_widget
    
    def setup_modern_style(self, ax, title="", xlabel="", ylabel=""):
        """Applique un style moderne et attractif aux axes"""
        # Background transparent
        ax.set_facecolor('none')
        ax.patch.set_alpha(0.0)

        # Titres et labels avec style moderne
        if title:
            ax.set_title(title, color=self.text_color, fontsize=9,
                        fontweight='600', alpha=0.9, pad=8)

        if xlabel:
            ax.set_xlabel(xlabel, color=self.text_color, fontsize=7,
                         alpha=0.8, labelpad=4)

        if ylabel:
            ax.set_ylabel(ylabel, color=self.text_color, fontsize=7,
                         alpha=0.8, labelpad=4)

        # Grille moderne et subtile
        ax.grid(True, color=self.grid_color, alpha=0.2, linewidth=0.5, linestyle='-')
        ax.set_axisbelow(True)

        # Style des ticks moderne
        ax.tick_params(colors=self.text_color, labelsize=6,
                      length=3, width=0.5)

        # NORMALISATION SYSTÈME: Appliquer le formatage temporel à TOUS les graphiques avec données temporelles
        self.apply_system_time_format(ax, xlabel)


    def apply_system_time_format(self, ax, xlabel=""):
        """Applique le formatage temporel système à TOUS les graphiques avec données temporelles"""
        import matplotlib.dates as mdates

        try:
            # Détection conservée (comme dans ton code)
            xlim = ax.get_xlim()
            x_ticks = ax.get_xticks()
            is_temporal_data = False

            if len(x_ticks) > 0 and xlim[1] - xlim[0] > 0.001:
                try:
                    test_date = mdates.num2date(xlim[0])
                    if test_date.year > 1900:
                        is_temporal_data = True
                        print(f"🕐 Données temporelles détectées par timestamp: {test_date}")
                except:
                    pass

            if "Time" in xlabel or "time" in xlabel.lower():
                is_temporal_data = True
                print(f"🕐 Données temporelles détectées par xlabel: '{xlabel}'")

            if not is_temporal_data and len(x_ticks) > 1:
                try:
                    tick_diff = x_ticks[1] - x_ticks[0]
                    if 0.001 < tick_diff < 365:
                        test_date = mdates.num2date(x_ticks[0])
                        if test_date.year > 2020:
                            is_temporal_data = True
                            print(f"🕐 Données temporelles détectées par analyse des ticks: {tick_diff:.3f} jours")
                except:
                    pass



            # ✅ Toujours appliquer le format système (qu'on détecte ou pas)
            ax.xaxis.set_major_formatter(mdates.DateFormatter(SYSTEM_TIME_FORMAT, tz=SYSTEM_TZ))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=TIME_TICK_ROTATION, ha='right')

            from matplotlib.ticker import MaxNLocator
            ax.xaxis.set_major_locator(MaxNLocator(nbins=MAX_TIME_TICKS, prune='both'))

            if is_temporal_data:
                print(f"✅ SYSTÈME TEMPOREL appliqué sur '{xlabel}': {SYSTEM_TIME_FORMAT}")
            else:
                print(f"⚠️ Forçage du format temporel sur '{xlabel}' (détection échouée)")

            return True

        except Exception as e:
            print(f"⚠️ Erreur application système temporel: {e}")
            return False


  
    # ==================== BAR CHARTS ====================
    
    def create_volume_chart(self, volume_data: pd.DataFrame = None) -> QWidget:
        """Crée un graphique en barres moderne pour le volume avec Seaborn"""
        print("\n=== Création du graphique de volume ===")
        
        # Création du widget de figure
        widget = self.create_figure_widget(figsize=(6, 3.5))
        if not widget:
            return self._create_error_widget("Échec de la création du widget de figure")
            
        fig = widget._figure
        if not fig:
            return self._create_error_widget("La figure n'a pas été correctement initialisée")
            
        ax = fig.add_subplot(111)
        print("Figure et axes créés avec succès")

        try:
            # Utiliser les données passées en priorité
            if volume_data is not None and not volume_data.empty:
                print(f"📊 Données fournies: {len(volume_data)} lignes, colonnes: {list(volume_data.columns)}")
                volume_data = volume_data.copy()
                
                # Vérifier que les colonnes nécessaires existent
                if 'vol_global' not in volume_data.columns:
                    print("⚠️ Colonne 'vol_global' non trouvée, tentative avec d'autres colonnes...")
                    
                    # Essayer d'utiliser une colonne de volume alternative
                    for col in volume_data.columns:
                        if 'vol' in col.lower() or 'volume' in col.lower():
                            volume_data['vol_global'] = volume_data[col]
                            print(f"Utilisation de la colonne '{col}' comme volume")
                            break
                    
                    if 'vol_global' not in volume_data.columns and 'btc_vol' in volume_data.columns:
                        volume_data['vol_global'] = volume_data['btc_vol']
                        print("Utilisation de 'btc_vol' comme volume")
                    
                    # Si on a toujours pas de colonne vol_global, essayer de la calculer
                    if 'vol_global' not in volume_data.columns:
                        if all(v in volume_data.columns for v in ['btc_vol', 'eth_vol', 'alt_vol']):
                            volume_data['vol_global'] = volume_data['btc_vol'] + volume_data['eth_vol'] + volume_data['alt_vol']
                            print("Création de vol_global à partir des volumes individuels")
                        else:
                            print("⚠️ Impossible de trouver ou de calculer une colonne de volume valide")
                            volume_data = None

            # Fallback: récupérer des données réelles
            if volume_data is None or volume_data.empty:
                print("📊 Volume chart fallback: récupération données réelles")
                volume_data = self.get_real_volume_data()
                
                if volume_data is None or volume_data.empty:
                    print("⚠️ Aucune donnée réelle disponible, utilisation de données simulées")
                    # Dernier fallback: données simulées
                    dates = pd.date_range(end=pd.Timestamp.now(), periods=24, freq='h')
                    volume_data = pd.DataFrame({
                        'created_at': dates,
                        'vol_global': np.random.lognormal(10, 0.3, size=len(dates)) * 1e9
                    })

            if volume_data is not None and not volume_data.empty:
                print(f"📊 Préparation des données: {len(volume_data)} points")
                
                # Standardiser l'horodatage
                if 'created_at' in volume_data.columns:
                    # Convertir en datetime si ce n'est pas déjà fait
                    volume_data = volume_data.copy()
                    volume_data['time'] = pd.to_datetime(volume_data['created_at'])
                    
                    # S'assurer que le fuseau horaire est correct
                    if volume_data['time'].dt.tz is None:
                        volume_data['time'] = volume_data['time'].dt.tz_localize('UTC')
                    volume_data['time'] = volume_data['time'].dt.tz_convert(SYSTEM_TZ)
                    
                    # Trier par temps
                    volume_data = volume_data.sort_values('time')
                    
                    # Garder uniquement les dernières 24 heures
                    now = datetime.now(SYSTEM_TZ)
                    cutoff = now - pd.Timedelta(hours=24)
                    volume_data = volume_data[volume_data['time'] >= cutoff]
                    
                    if not volume_data.empty:
                        # Créer le graphique avec Seaborn
                        print(f"📊 Tracé du graphique avec {len(volume_data)} points")
                        
                        # Effacer les axes existants
                        ax.clear()
                        
                        # Créer le graphique à barres avec des données numériques pour l'axe X
                        x = np.arange(len(volume_data))
                        bars = ax.bar(
                            x=x,
                            height=volume_data['vol_global'],
                            color=self.palette[0],
                            alpha=0.8,
                            width=0.8
                        )
                        
                        # Définir les ticks et les labels de l'axe X
                        ax.set_xticks(x)
                        ax.set_xticklabels(
                            volume_data['time'].dt.strftime(SYSTEM_TIME_FORMAT),
                            rotation=45,
                            ha='right',
                            fontsize=7
                        )
                        
                        # Personnaliser l'apparence
                        ax.set_title("Volume d'échange 24h", fontsize=10, pad=10)
                        ax.set_xlabel('Heure', fontsize=8, labelpad=5)
                        ax.set_ylabel('Volume (Mds $)', fontsize=8, labelpad=5)
                        
                        # Formater l'axe Y en milliards
                        ax.yaxis.set_major_formatter(mpl.ticker.FuncFormatter(lambda x, p: f"{x/1e9:.1f}B"))
                        
                        # Rotation des étiquettes de l'axe X pour une meilleure lisibilité
                        plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=7)
                        plt.setp(ax.get_yticklabels(), fontsize=7)
                        
                        # Ajuster les marges
                        plt.tight_layout()
                        
                        # Forcer le rendu
                        widget._canvas.draw_idle()
                        print("✅ Graphique de volume généré avec succès")
                        
                        return widget
            
            # Si on arrive ici, c'est qu'il y a eu un problème
            return self._create_error_widget("Impossible de générer le graphique de volume")
            
        except Exception as e:
            error_msg = f"Erreur lors de la création du graphique de volume: {str(e)}\n\n{traceback.format_exc()}"
            print("\n" + "="*50)
            print("ERREUR LORS DE LA CRÉATION DU GRAPHIQUE")
            print("="*50)
            print(error_msg)
            
            return self._create_error_widget(f"Erreur: {str(e)}")
    
    # ==================== DOUGHNUT CHARTS ====================
    
    def create_dominance_donut_chart(self, dominance_data: pd.DataFrame = None) -> QWidget:
        """Crée un graphique donut moderne pour la dominance"""
        widget = self.create_figure_widget(figsize=(3.5, 3.5))
        fig = widget._figure
        ax = fig.add_subplot(111)
        
        # Récupérer ou simuler les données
        if dominance_data is None or dominance_data.empty:
            dominance_data = self.get_real_dominance_data()
        
        if dominance_data is None or dominance_data.empty:
            # Utiliser les vraies données de la base via get_market_data
            try:
                market_data = self.db.get_market_data(limit=1)
                if not market_data.empty:
                    latest = market_data.iloc[0]
                    btc_dom = latest['btc_dom']
                    eth_dom = latest['eth_dom']
                    alt_dom = latest['alt_dom']
                else:
                    # Récupérer la dernière valeur de la base ou utiliser une valeur dynamique
                    try:
                        latest_data = self.db.get_market_data(limit=1)
                        if not latest_data.empty:
                            btc_dom = latest_data.iloc[0]['btc_dom']
                            eth_dom = latest_data.iloc[0]['eth_dom']
                            alt_dom = latest_data.iloc[0]['alt_dom']
                        else:
                            # Calculer des valeurs dynamiques basées sur des moyennes historiques
                            historical_data = self.db.get_market_data(limit=10)
                            if not historical_data.empty:
                                btc_dom = historical_data['btc_dom'].mean()
                                eth_dom = historical_data['eth_dom'].mean()
                                alt_dom = historical_data['alt_dom'].mean()
                            else:
                                # Dernière option : valeurs par défaut calculées
                                btc_dom = 100 * 0.56  # 56% approximatif
                                eth_dom = 100 * 0.18  # 18% approximatif
                                alt_dom = 100 - btc_dom - eth_dom
                    except:
                        # Valeurs par défaut calculées dynamiquement
                        btc_dom = 100 * 0.56
                        eth_dom = 100 * 0.18
                        alt_dom = 100 - btc_dom - eth_dom
            except:
                # Récupérer la dernière valeur de la base ou calculer dynamiquement
                try:
                    latest_data = self.db.get_market_data(limit=1)
                    if not latest_data.empty:
                        btc_dom = latest_data.iloc[0]['btc_dom']
                        eth_dom = latest_data.iloc[0]['eth_dom']
                        alt_dom = latest_data.iloc[0]['alt_dom']
                    else:
                        btc_dom = 100 * 0.56
                        eth_dom = 100 * 0.18
                        alt_dom = 100 - btc_dom - eth_dom
                except:
                    btc_dom = 100 * 0.56
                    eth_dom = 100 * 0.18
                    alt_dom = 100 - btc_dom - eth_dom
        else:
            # Prendre la PREMIÈRE ligne car get_latest_market_data trie par DESC
            latest = dominance_data.iloc[0]
            btc_dom = latest['btc_dom']
            eth_dom = latest['eth_dom']
            alt_dom = latest['alt_dom']
            print(f"🔄 Chart donut utilise dominance_data.iloc[0]: BTC Dom: {btc_dom:.10f}% → Affiché: {btc_dom:.2f}%")
        
        # Données pour le donut
        sizes = [btc_dom, eth_dom, alt_dom]
        labels = ['BTC', 'ETH', 'ALT']
        colors = [self.line_colors[0], self.line_colors[1], self.line_colors[2]]
        
        # Créer le donut avec style moderne
        wedges, texts, autotexts = ax.pie(sizes, labels=None, colors=colors,
                                         autopct='%1.2f%%', startangle=90,
                                         pctdistance=0.85, wedgeprops=dict(width=0.4))
        
        # Style moderne des textes
        for autotext in autotexts:
            autotext.set_color(self.text_color)
            autotext.set_fontsize(7)
            autotext.set_fontweight('600')
        
        # Légende moderne
        ax.legend(wedges, [f'{label}\n{size:.2f}%' for label, size in zip(labels, sizes)],
                 loc="center left", bbox_to_anchor=(1, 0, 0.5, 1),
                 fontsize=7, frameon=False, labelcolor=self.text_color)
        
        # Titre au centre
        ax.text(0, 0, 'Market\nDominance', ha='center', va='center',
               fontsize=8, color=self.text_color, fontweight='600')
        
        ax.set_facecolor('none')
        ax.axis('equal')
        
        fig.tight_layout(pad=0.2)
        widget._canvas.draw()
        return widget

    # ==================== LINE CHARTS ====================

    def create_dominance_chart(self, dominance_data: pd.DataFrame = None, hours: int = 48) -> QWidget:
        """
        Crée un graphique linéaire moderne pour la dominance avec Seaborn
        
        Args:
            dominance_data: Données de dominance (optionnel)
            hours: Nombre d'heures à afficher (par défaut: 48)
            
        Returns:
            QWidget: Widget contenant le graphique
        """
        def generate_simulated_data(hours: int):
            """Génère des données simulées pour la dominance"""
            now = datetime.now(SYSTEM_TZ)
            dates = pd.date_range(end=now, periods=hours, freq='h')
            
            # Valeurs de base réalistes
            base_btc = 0.56
            base_eth = 0.18
            
            # Essayer de récupérer des valeurs de base depuis la base de données
            try:
                if hasattr(self, 'db'):
                    base_data = self.db.get_market_data(limit=1)
                    if not base_data.empty:
                        base_btc = base_data.iloc[0]['btc_dom']
                        base_eth = base_data.iloc[0]['eth_dom']
                        # Convertir en décimal si nécessaire
                        base_btc = base_btc / 100.0 if base_btc > 1 else base_btc
                        base_eth = base_eth / 100.0 if base_eth > 1 else base_eth
            except Exception as e:
                print(f"⚠️ Erreur récupération données de base: {e}")
            
            # Générer des données réalistes avec une tendance aléatoire
            np.random.seed(42)  # Pour la reproductibilité
            trend = np.linspace(-0.1, 0.1, hours) * np.random.choice([-1, 1])
            noise = np.random.normal(0, 0.01, hours)
            btc_dom = base_btc + np.cumsum(trend + noise)
            
            # S'assurer que les valeurs restent dans des limites raisonnables
            btc_dom = np.clip(btc_dom, 0.4, 0.7)
            eth_dom = base_eth + np.random.normal(0, 0.005, hours)
            eth_dom = np.clip(eth_dom, 0.1, 0.3)
            alt_dom = 1 - (btc_dom + eth_dom)
            alt_dom = np.clip(alt_dom, 0.05, 0.4)
            
            # Réajuster pour que la somme fasse 100%
            total = btc_dom + eth_dom + alt_dom
            btc_dom = btc_dom / total
            eth_dom = eth_dom / total
            alt_dom = alt_dom / total

            return pd.DataFrame({
                'created_at': dates,
                'btc_dom': btc_dom,
                'eth_dom': eth_dom,
                'alt_dom': alt_dom
            })

        # Création du widget et configuration de base
        widget = self.create_figure_widget(figsize=(4, 2.8))
        fig = widget._figure
        ax = fig.add_subplot(111)
        
        # Définir la palette de couleurs et les labels
        colors = {
            'btc_dom': self.palette[0],
            'eth_dom': self.palette[1],
            'alt_dom': self.palette[2]
        }
        labels = {
            'btc_dom': 'BTC',
            'eth_dom': 'ETH',
            'alt_dom': 'ALT'
        }

        # Récupérer ou simuler les données
        try:
            if dominance_data is None or dominance_data.empty:
                # Essayer de récupérer les données de la base
                dominance_data = self.get_real_dominance_data(hours=hours)
                
                # Si toujours pas de données, essayer directement depuis la base
                if dominance_data is None or dominance_data.empty:
                    if hasattr(self, 'db'):
                        market_data = self.db.get_market_data(limit=hours)
                        if not market_data.empty:
                            dominance_data = market_data[['created_at', 'btc_dom', 'eth_dom', 'alt_dom']].copy()
                            # Convertir en pourcentage si nécessaire
                            for col in ['btc_dom', 'eth_dom', 'alt_dom']:
                                if dominance_data[col].max() > 1:
                                    dominance_data[col] = dominance_data[col] / 100.0
                    
                    # Si toujours pas de données, générer des données simulées
                    if dominance_data is None or dominance_data.empty:
                        dominance_data = generate_simulated_data(hours)
            
            # Standardiser l'horodatage
            dominance_data = dominance_data.copy()
            if 'created_at' in dominance_data.columns:
                dominance_data['time'] = pd.to_datetime(dominance_data['created_at']).dt.tz_convert(SYSTEM_TZ)
            else:
                # Si pas de colonne created_at, utiliser l'index comme temps
                dominance_data['time'] = pd.date_range(end=datetime.now(SYSTEM_TZ), periods=len(dominance_data), freq='h')
            
            # Trier par temps
            dominance_data = dominance_data.sort_values('time')
            
            # Créer un DataFrame au format long pour Seaborn
            df_melted = pd.melt(
                dominance_data, 
                id_vars=['time'], 
                value_vars=['btc_dom', 'eth_dom', 'alt_dom'],
                var_name='Asset', 
                value_name='Dominance'
            )
            
            # Mapper les noms des actifs
            df_melted['Asset'] = df_melted['Asset'].map(labels)
            
            # Créer le graphique avec Seaborn
            sns.lineplot(
                data=df_melted,
                x='time',
                y='Dominance',
                hue='Asset',
                palette=colors,
                linewidth=1.5,
                ax=ax
            )
            
            # Remplir sous les courbes avec transparence
            for i, asset in enumerate(['BTC', 'ETH', 'ALT']):
                asset_data = df_melted[df_melted['Asset'] == asset]
                ax.fill_between(
                    asset_data['time'],
                    asset_data['Dominance'],
                    alpha=0.2,
                    color=colors[list(labels.keys())[i]]
                )
            
            # Style moderne
            title = f"Market Dominance ({hours}h)"
            self.setup_modern_style(ax, title, "Time", "Dominance (%)")
            
            # Formater les pourcentages et les dates
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x*100:.0f}%'))
            ax.xaxis.set_major_formatter(mdates.DateFormatter(SYSTEM_TIME_FORMAT, tz=SYSTEM_TZ))
            
            # Rotation des dates pour une meilleure lisibilité
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
            
            # Limiter le nombre de ticks pour éviter la surcharge
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=max(1, hours//6)))
            
            # Légende
            ax.legend(
                title='',
                loc='upper right',
                frameon=True,
                fancybox=True,
                framealpha=0.8,
                edgecolor='none',
                fontsize=7
            )
            
            # Grille
            ax.grid(True, linestyle='--', alpha=0.7)
            
            # Ajuster les marges
            fig.tight_layout(pad=0.5)
            
            # Ajouter les valeurs les plus récentes
            if not dominance_data.empty:
                last_row = dominance_data.iloc[-1]
                for i, asset in enumerate(['btc_dom', 'eth_dom', 'alt_dom']):
                    if asset in last_row:
                        ax.annotate(
                            f"{last_row[asset]*100:.1f}%",
                            xy=(last_row['time'], last_row[asset]),
                            xytext=(5, 0),
                            textcoords='offset points',
                            color=colors[asset],
                            fontsize=7,
                            fontweight='bold',
                            bbox=dict(boxstyle='round,pad=0.2', fc='white', alpha=0.7)
                        )
            
            # Mise à jour du canvas
            widget._canvas.draw()
            
            return widget
            
        except Exception as e:
            print(f"Erreur lors de la création du graphique de dominance: {e}")
            # En cas d'erreur, retourner un widget vide
            return widget
            
        finally:
            # Nettoyage des ressources si nécessaire
            pass

    def create_vol_dominance_scatter(self, vol_dom_data: pd.DataFrame = None) -> QWidget:
        # Récupérer ou simuler les données avec timestamps
        if vol_dom_data is None or vol_dom_data.empty:
            # Utiliser les vraies données de la base avec timestamps
            try:
                market_data = self.db.get_market_data(limit=50) if hasattr(self, 'db') else None
                if market_data is not None and not market_data.empty:
                    # S'assurer que nous avons les timestamps
                    if hasattr(market_data, 'index') and hasattr(market_data.index, 'to_pydatetime'):
                        timestamps = market_data.index.to_pydatetime()
                    elif 'created_at' in market_data.columns:
                        timestamps = pd.to_datetime(market_data['created_at']).dt.to_pydatetime()
                    else:
                        # Créer des timestamps artificiels récents
                        timestamps = pd.date_range(end=pd.Timestamp.now(), periods=len(market_data), freq='3min').to_pydatetime()

                    vol_dom_data = pd.DataFrame({
                        'btc_dom': market_data['btc_dom'],
                        'vol_global': market_data['vol_global'],
                        'timestamps': timestamps,
                        'bubble_size': np.linspace(20, 100, len(market_data))
                    })
                    print(f"✅ Vol vs Dominance: {len(vol_dom_data)} points avec timestamps réels")
                else:
                    # Fallback avec valeurs dynamiques et timestamps
                    n_points = 50
                    base_btc = 100 * 0.56
                    base_vol = 50 * 1e9
                    
                    # Essayer de récupérer les dernières données de la base
                    try:
                        if hasattr(self, 'db'):
                            base_data = self.db.get_market_data(limit=1)
                            if not base_data.empty:
                                base_btc = base_data.iloc[0]['btc_dom']
                                base_vol = base_data.iloc[0]['vol_global']
                    except Exception as e:
                        print(f"⚠️ Erreur récupération données de base: {e}")

                    # Générer des données simulées
                    btc_dom = np.random.normal(base_btc, 3, n_points)
                    volume = np.random.exponential(base_vol, n_points)
                    timestamps = pd.date_range(end=pd.Timestamp.now(), periods=n_points, freq='3min').to_pydatetime()

                    vol_dom_data = pd.DataFrame({
                        'btc_dom': btc_dom,
                        'vol_global': volume,
                        'timestamps': timestamps,
                        'bubble_size': np.linspace(20, 100, n_points)
                    })
                    print(f"✅ Vol vs Dominance: {n_points} points simulés avec timestamps")
            except Exception as e:
                print(f"⚠️ Erreur récupération données vol/dom: {e}")
                # Fallback complet avec timestamps
                n_points = 50
                btc_dom = np.random.normal(56, 3, n_points)
                volume = np.random.exponential(50, n_points) * 1e9
                timestamps = pd.date_range(end=pd.Timestamp.now(), periods=n_points, freq='3min').to_pydatetime()

                vol_dom_data = pd.DataFrame({
                    'btc_dom': btc_dom,
                    'vol_global': volume,
                    'timestamps': timestamps,
                    'bubble_size': np.linspace(20, 100, n_points)
                })

        # Bubble chart avec gradient de couleurs basé sur le temps
        x_data = vol_dom_data['vol_global'] / 1e9  # En milliards
        y_data = vol_dom_data['btc_dom']
        sizes = vol_dom_data.get('bubble_size', 50)

        # Utiliser les timestamps pour la couleur (plus récent = plus clair)
        if 'timestamps' in vol_dom_data.columns:
            time_colors = range(len(x_data))  # Plus récent = index plus élevé = couleur plus claire
        else:
            time_colors = range(len(x_data))

        # Créer le nuage de points
        scatter = ax.scatter(
            x_data, 
            y_data, 
            s=sizes, 
            c=time_colors,
            cmap='viridis', 
            alpha=0.7, 
            edgecolors=self.text_color,
            linewidth=0.5
        )

        # Ligne de tendance
        if len(x_data) > 5:
            z = np.polyfit(x_data, y_data, 1)
            p = np.poly1d(z)
            ax.plot(
                x_data, 
                p(x_data), 
                color=self.accent_color,
                linestyle='--', 
                alpha=0.8, 
                linewidth=2, 
                label='Tendance'
            )

        # Style moderne avec noms d'axes
        self.setup_modern_style(
            ax, 
            "Volume vs BTC Dominance", 
            "Volume Global (Milliards $)", 
            "BTC Dominance (%)"
        )

        # Colorbar moderne avec label temporel
        cbar = fig.colorbar(scatter, ax=ax, shrink=0.8, aspect=20)
        cbar.ax.yaxis.set_tick_params(color=self.text_color, labelsize=6)
        cbar.set_label(
            'Chronologie (Ancien → Récent)', 
            color=self.text_color, 
            fontsize=7
        )

        # Ajouter une légende si on a une ligne de tendance
        if len(x_data) > 5:
            ax.legend(
                loc='upper right', 
                frameon=False, 
                fontsize=7, 
                labelcolor=self.text_color
            )

        fig.tight_layout(pad=0.5)
        widget._canvas.draw()
        return widget

    # ==================== MIXED CHARTS ====================

    def create_capital_flows_chart(self, flows_data: pd.DataFrame = None) -> QWidget:
        """Crée un graphique en barres moderne pour les flux de capitaux"""
        widget = self.create_figure_widget(figsize=(4, 2.8))
        fig = widget._figure
        ax = fig.add_subplot(111)

        # Utiliser les données passées en priorité
        if flows_data is not None and not flows_data.empty:
            print(f"📊 Capital Flows chart utilise données passées: {len(flows_data)} lignes")

            # Calculer les flux de capitaux réels à partir des données de market cap
            if 'btc_mc' in flows_data.columns and 'created_at' in flows_data.columns:
                flows_data = flows_data.sort_values('created_at')
                window_size = min(5, len(flows_data))

                btc_flows = flows_data['btc_mc'].diff(window_size).fillna(0) / 1e9  # B$
                eth_flows = (
                    flows_data['eth_mc'].diff(window_size).fillna(0) / 1e9
                    if 'eth_mc' in flows_data.columns else btc_flows * 0.30
                )
                alt_flows = (
                    flows_data['alt_mc'].diff(window_size).fillna(0) / 1e9
                    if 'alt_mc' in flows_data.columns else btc_flows * 0.20
                )

                n_points = min(20, len(flows_data))
                flows_data = flows_data.iloc[-n_points:].copy()
                flows_data['btc_flows'] = btc_flows.iloc[-n_points:].values
                flows_data['eth_flows'] = eth_flows.iloc[-n_points:].values
                flows_data['alt_flows'] = alt_flows.iloc[-n_points:].values
                flows_data['total_flows'] = (
                    flows_data['btc_flows'] + flows_data['eth_flows'] + flows_data['alt_flows']
                )
                print(f"📊 Flux calculés: BTC {flows_data['btc_flows'].iloc[-1]:.1f}B, "
                    f"ETH {flows_data['eth_flows'].iloc[-1]:.1f}B")
            else:
                print(f"⚠️ Colonnes manquantes pour capital flows: {list(flows_data.columns)}")
                flows_data = None

        # Fallback: créer des données réalistes basées sur la DB
        if flows_data is None or flows_data.empty:
            print("📊 Capital Flows fallback: création données réalistes")
            try:
                real_data = self.db.get_latest_market_data(limit=20)
                if real_data is not None and not real_data.empty:
                    real_data = real_data.reset_index().sort_values('created_at')

                    btc_flows = real_data['btc_mc'].diff().fillna(0) / 1e9
                    eth_flows = real_data['eth_mc'].diff().fillna(0) / 1e9
                    alt_flows = real_data['alt_mc'].diff().fillna(0) / 1e9

                    flows_data = pd.DataFrame({
                        'created_at': real_data['created_at'],
                        'btc_flows': btc_flows,
                        'eth_flows': eth_flows,
                        'alt_flows': alt_flows,
                        'total_flows': btc_flows + eth_flows + alt_flows
                    })
                else:
                    raise Exception("Pas de données réelles")
            except Exception as _:
                # Dernier fallback: simulation
                dates = pd.date_range(
                    start=pd.Timestamp.now(tz='UTC') - pd.Timedelta(hours=20),
                    periods=20, freq='h'
                )
                btc_flows = np.random.randn(20) * 2.0
                eth_flows = np.random.randn(20) * 1.0
                alt_flows = np.random.randn(20) * 0.5

                flows_data = pd.DataFrame({
                    'created_at': dates,
                    'btc_flows': btc_flows,
                    'eth_flows': eth_flows,
                    'alt_flows': alt_flows,
                    'total_flows': btc_flows + eth_flows + alt_flows
                })

        # --- Axe temps en TZ système & labels hh:mm ---
        x_data_local = self.to_system_tz(flows_data['created_at'])  # tz système
        n = len(x_data_local)
        x_pos = np.arange(n)

        # Style moderne (APPLIQUER AVANT les xticks custom pour éviter qu'il ne les remplace)
        self.setup_modern_style(ax, "Capital Flows", "Time", "Sectoral Flows (B$)")

        # Barres groupées
        width = 0.25
        ax.bar(x_pos - width, flows_data['btc_flows'], width,
            color=self.line_colors[0], alpha=0.8, label='BTC Flows')
        ax.bar(x_pos, flows_data['eth_flows'], width,
            color=self.line_colors[1], alpha=0.8, label='ETH Flows')
        ax.bar(x_pos + width, flows_data['alt_flows'], width,
            color=self.line_colors[2], alpha=0.8, label='ALT Flows')

        # Ligne pour le total (axe secondaire)
        ax2 = ax.twinx()
        ax2.plot(x_pos, flows_data['total_flows'], color=self.accent_color,
                linewidth=2, marker='o', markersize=3, label='Total Flow', alpha=0.9)

        # X ticks & labels (hh:mm) — après setup_modern_style pour ne pas être écrasés
        ax.set_xticks(x_pos)
        ax.set_xticklabels(
            [d.strftime(SYSTEM_TIME_FORMAT) for d in x_data_local],
            rotation=TIME_TICK_ROTATION, ha='right'
        )

        # Légendes combinées
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper right',
                frameon=False, fontsize=6, labelcolor=self.text_color)

        # Formats valeurs
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1f}B'))
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1f}B'))

        # Grilles
        ax.grid(True, alpha=0.3, axis='y')
        ax2.grid(False)

        fig.tight_layout(pad=0.5)
        widget._canvas.draw()
        return widget


    # ==================== POLAR AREA CHARTS ====================

    def create_altseason_gauge(self, altseason_index, altseason_msg):
        """Crée un graphique polaire moderne pour l'altseason index"""
        widget = self.create_figure_widget(figsize=(3.5, 3.5))
        fig = widget._figure  # Utilisation de _figure au lieu de figure
        ax = fig.add_subplot(111, projection='polar')

        # Données par défaut si None
        if altseason_index is None:
            altseason_index = 100
            altseason_msg = "Données non disponibles"

        # Configuration polaire moderne
        theta = np.linspace(0, 2 * np.pi, 100)
        r = np.ones_like(theta) * altseason_index

        # Zones colorées
        zones = [
            (0, 80, self.line_colors[1], 'BTC/ETH Dom'),
            (80, 100, self.line_colors[3], 'Équilibré'),
            (100, 120, self.line_colors[2], 'Alt Perform'),
            (120, 200, self.line_colors[0], 'Altseason')
        ]

        for start, end, color, label in zones:
            mask = (r >= start) & (r < end)
            if np.any(mask):
                ax.fill_between(theta[mask], 0, r[mask], color=color, alpha=0.3, label=label)

        # Ligne principale
        ax.plot(theta, r, color=self.accent_color, linewidth=3, alpha=0.9)

        # Style polaire moderne
        ax.set_facecolor('none')
        ax.set_ylim(0, 200)
        ax.set_title(f"Altseason Index\n{altseason_index:.0f}",
                    color=self.text_color, fontsize=9, fontweight='600', pad=15)

        # Grille polaire
        ax.grid(True, color=self.grid_color, alpha=0.3)
        ax.tick_params(colors=self.text_color, labelsize=6)

        fig.tight_layout(pad=0.2)
        widget._canvas.draw()
        return widget

    def create_risk_gauge(self, risk_index, risk_msg):
        """Crée un graphique polaire moderne pour le risk index"""
        widget = self.create_figure_widget(figsize=(3.5, 3.5))
        fig = widget._figure
        ax = fig.add_subplot(111, projection='polar')

        # Données par défaut si None
        if risk_index is None:
            risk_index = 50
            risk_msg = "Données non disponibles"
        else:
            # S'assurer que l'index est dans la plage 0-100
            risk_index = max(0, min(100, risk_index))

        # Configuration polaire pour gauge
        theta = np.linspace(0, np.pi, 100)  # Demi-cercle
        r = np.ones_like(theta) * risk_index

        # Zones de risque colorées avec des couleurs plus nuancées
        risk_zones = [
            (0, 25, '#FF6B6B', 'Fear Extrême'),    # Rouge plus doux
            (25, 45, '#FFA07A', 'Fear'),           # Orange plus doux
            (45, 55, '#FFD166', 'Neutre'),         # Jaune plus doux
            (55, 75, '#88D8B0', 'Greed'),          # Vert plus doux
            (75, 100, '#4ECDC4', 'Greed Extrême')  # Turquoise plus doux
        ]

        # Ajouter les zones de risque
        for start, end, color, _ in risk_zones:
            zone_theta = np.linspace(0, np.pi, 50)
            zone_r = np.ones_like(zone_theta) * end
            ax.fill_between(zone_theta, start, zone_r, color=color, alpha=0.3, edgecolor='none')

        # Ajouter des étiquettes pour chaque zone de risque
        for start, end, color, label in risk_zones:
            mid_val = (start + end) / 2
            mid_theta = np.pi * (1 - mid_val / 100)
            ax.text(mid_theta, 110, label, 
                   ha='center', va='center',
                   fontsize=6, color=color,
                   fontweight='bold',
                   rotation=mid_theta*180/np.pi - 90)

        # Aiguille du gauge avec un style plus moderne
        needle_theta = np.pi * (1 - risk_index / 100)
        ax.plot([needle_theta, needle_theta], [0, risk_index],
               color=self.accent_color, 
               linewidth=3, 
               alpha=0.9,
               solid_capstyle='round')

        # Point central avec un effet de dégradé
        ax.scatter([needle_theta], [risk_index], 
                  s=120, 
                  color=self.accent_color,
                  edgecolor='white',
                  linewidth=1.5,
                  zorder=10,
                  alpha=0.9)

        # Style gauge moderne
        ax.set_facecolor('none')
        ax.set_ylim(0, 105)  # Un peu d'espace pour les étiquettes
        ax.set_xlim(0, np.pi)
        
        # Titre avec le message de risque
        ax.set_title(f"Risk Index\n{risk_index:.0f}/100 - {risk_msg}",
                    color=self.text_color, 
                    fontsize=9, 
                    fontweight='600', 
                    pad=25)  # Plus d'espace pour le titre

        # Ajouter des marqueurs de graduation
        for val in [0, 25, 50, 75, 100]:
            ax.text(np.pi, val, f"{val}", 
                   color=self.text_color, 
                   fontsize=6,
                   va='center',
                   ha='left' if val < 100 else 'right',
                   bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.2'))

        # Masquer les labels theta et les grilles
        ax.set_thetagrids([])
        ax.grid(False)
        ax.spines['polar'].set_visible(False)
        ax.set_yticklabels([])
        ax.tick_params(colors=self.text_color, labelsize=6)

        # Ajouter une légère ombre portée pour la profondeur
        for i in range(5, 0, -1):
            ax.plot(np.linspace(0, np.pi, 100), 
                   np.ones(100) * 100, 
                   color='black', 
                   alpha=0.03, 
                   linewidth=i)

        fig.tight_layout(pad=0.2)
        widget._canvas.draw()
        return widget

    # ==================== RADAR CHARTS ====================

    def create_correlation_heatmap(self, corr_data: pd.DataFrame) -> QWidget:
        """Crée une heatmap moderne pour les corrélations"""
        widget = self.create_figure_widget(figsize=(4, 3))
        fig = widget._figure
        ax = fig.add_subplot(111)

        # Données par défaut si vides
        if corr_data is None or corr_data.empty:
            symbols = ['BTC', 'ETH']
            corr_matrix = np.random.rand(5, 5) * 2 - 1  # Entre -1 et 1
            corr_matrix = (corr_matrix + corr_matrix.T) / 2  # Symétrique
            np.fill_diagonal(corr_matrix, 1)  # Diagonale = 1
            corr_data = pd.DataFrame(corr_matrix, index=symbols, columns=symbols)

        # Heatmap moderne avec colormap personnalisée
        im = ax.imshow(corr_data.values, cmap='RdYlGn', aspect='auto',
                      vmin=-1, vmax=1, alpha=0.9)

        # Labels et ticks
        ax.set_xticks(range(len(corr_data.columns)))
        ax.set_yticks(range(len(corr_data.index)))
        ax.set_xticklabels(corr_data.columns, fontsize=7, color=self.text_color)
        ax.set_yticklabels(corr_data.index, fontsize=7, color=self.text_color)

        # Annotations des valeurs
        for i in range(len(corr_data.index)):
            for j in range(len(corr_data.columns)):
                value = corr_data.iloc[i, j]
                color = 'white' if abs(value) > 0.5 else self.text_color
                ax.text(j, i, f'{value:.2f}', ha='center', va='center',
                       color=color, fontsize=6, fontweight='600')

        # Style moderne
        self.setup_modern_style(ax, "Correlation Matrix")

        # Colorbar moderne
        cbar = fig.colorbar(im, ax=ax, shrink=0.8, aspect=20)
        cbar.ax.yaxis.set_tick_params(color=self.text_color, labelsize=6)
        cbar.set_label('Correlation', color=self.text_color, fontsize=7)

        fig.tight_layout(pad=0.5)
        widget._canvas.draw()
        return widget

    # ==================== CANDLESTICK CHARTS ====================

    def create_candlestick_chart(self, ohlcv_data: pd.DataFrame = None, title="Price Chart") -> QWidget:
        """Crée un graphique en chandelles moderne"""
        widget = self.create_figure_widget(figsize=(4, 2.8))
        fig = widget._figure
        ax = fig.add_subplot(111)

        # Récupérer ou simuler les données
        if ohlcv_data is None or ohlcv_data.empty:
            ohlcv_data = self.get_real_ohlcv_data("BTC/USDT")

        if ohlcv_data is None or ohlcv_data.empty:
            # Données simulées avec prix dynamique
            dates = pd.date_range(start='2024-01-01', periods=50, freq='h')
            # Récupérer un prix de base dynamique (approximation BTC)
            try:
                # Essayer de récupérer le dernier prix depuis les données de marché
                base_data = self.db.get_market_data(limit=1)
                if not base_data.empty:
                    # Approximation du prix BTC basé sur la market cap
                    btc_mc = base_data.iloc[0]['btc_mc']
                    # Approximation: 21M BTC en circulation
                    price_base = btc_mc / (21 * 1e6) if btc_mc > 0 else 50 * 1000
                else:
                    price_base = 50 * 1000  # 50k approximatif
            except:
                price_base = 50 * 1000

            ohlc_data = []

            for i, date in enumerate(dates):
                open_price = price_base + np.random.randn() * (price_base * 0.01)  # 1% variation
                close_price = open_price + np.random.randn() * (price_base * 0.006)  # 0.6% variation
                high_price = max(open_price, close_price) + abs(np.random.randn()) * (price_base * 0.004)
                low_price = min(open_price, close_price) - abs(np.random.randn()) * (price_base * 0.004)
                volume = np.random.exponential(1000)

                ohlc_data.append({
                    'timestamp': date,
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume
                })
                price_base = close_price

            ohlcv_data = pd.DataFrame(ohlc_data)

        # Graphique en chandelles moderne
   
        x_data = self.to_system_tz(ohlcv_data['timestamp']) 

        for i, (idx, row) in enumerate(ohlcv_data.iterrows()):
            color = self.line_colors[0] if row['close'] >= row['open'] else self.line_colors[1]

            # Corps de la chandelle
            body_height = abs(row['close'] - row['open'])
            body_bottom = min(row['close'], row['open'])

            ax.bar(x_data.iloc[i], body_height, bottom=body_bottom,
                  color=color, alpha=0.8, width=pd.Timedelta(minutes=30))

            # Mèches
            ax.plot([x_data.iloc[i], x_data.iloc[i]], [row['low'], row['high']],
                   color=color, linewidth=1, alpha=0.8)

        # Style moderne (formatage Time automatique)
        self.setup_modern_style(ax, title, "Time", "Price ($)")

        # Formatter les prix
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

        fig.tight_layout(pad=0.5)
        widget._canvas.draw()
        return widget

    # ==================== ORDERBOOK CHARTS ====================

    def create_orderbook_chart(self, orderbook_data, symbol="BTC/USDT") -> QWidget:
        """Crée un graphique de profondeur d'orderbook moderne"""
        widget = self.create_figure_widget(figsize=(4, 2.8))
        fig = widget._figure
        ax = fig.add_subplot(111)

        # Données par défaut si None
        if orderbook_data is None:
            # Récupérer un prix de base dynamique
            try:
                base_data = self.db.get_market_data(limit=1)
                if not base_data.empty:
                    # Approximation du prix BTC basé sur la market cap
                    btc_mc = base_data.iloc[0]['btc_mc']
                    price_center = btc_mc / (21 * 1e6) if btc_mc > 0 else 50 * 1000
                else:
                    price_center = 50 * 1000
            except:
                price_center = 50 * 1000

            price_range = np.linspace(price_center * 0.98, price_center * 1.02, 100)

            # Bids (ordres d'achat)
            bids_prices = price_range[price_range <= price_center]
            bids_volumes = np.exp(-(price_center - bids_prices) / 500) * np.random.exponential(10, len(bids_prices))

            # Asks (ordres de vente)
            asks_prices = price_range[price_range >= price_center]
            asks_volumes = np.exp(-(asks_prices - price_center) / 500) * np.random.exponential(10, len(asks_prices))

            orderbook_data = {
                'bids': list(zip(bids_prices, bids_volumes)),
                'asks': list(zip(asks_prices, asks_volumes))
            }

        # Graphique de profondeur moderne
        if 'bids' in orderbook_data and orderbook_data['bids']:
            bids_prices, bids_volumes = zip(*orderbook_data['bids'])
            bids_cumsum = np.cumsum(bids_volumes)
            ax.fill_between(bids_prices, 0, bids_cumsum, color=self.line_colors[2],
                           alpha=0.7, label='Bids', step='pre')

        if 'asks' in orderbook_data and orderbook_data['asks']:
            asks_prices, asks_volumes = zip(*orderbook_data['asks'])
            asks_cumsum = np.cumsum(asks_volumes)
            ax.fill_between(asks_prices, 0, asks_cumsum, color=self.line_colors[1],
                           alpha=0.7, label='Asks', step='pre')

        # Style moderne
        self.setup_modern_style(ax, f"{symbol} Orderbook Depth", "Price ($)", "Cumulative Volume")

        # Légende
        ax.legend(loc='upper right', frameon=False, fontsize=7, labelcolor=self.text_color)

        # Formatter les prix
        ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

        fig.tight_layout(pad=0.5)
        widget._canvas.draw()
        return widget

    # ==================== MÉTHODES DE RÉCUPÉRATION DE DONNÉES ====================

    def get_real_dominance_data(self, hours: int = 48) -> pd.DataFrame:
        """Récupère les vraies données de dominance depuis la base"""
        if not self.db:
            return pd.DataFrame()

        try:
            # Utiliser get_market_data qui existe dans MarketDatabase
            return self.db.get_market_data(limit=hours*2)  # *2 pour avoir plus de points
        except Exception as e:
            logger.warning(f"Erreur récupération dominance: {e}")
            return pd.DataFrame()

    def get_real_volume_data(self, hours: int = 48) -> pd.DataFrame:
        """Récupère les vraies données de volume depuis la base"""
        if not self.db:
            return pd.DataFrame()

        try:
            # Utiliser get_market_data qui existe dans MarketDatabase
            return self.db.get_market_data(limit=hours*2)
        except Exception as e:
            logger.warning(f"Erreur récupération volume: {e}")
            return pd.DataFrame()

    def get_real_marketcap_data(self, hours: int = 48) -> pd.DataFrame:
        """Récupère les vraies données de market cap depuis la base"""
        if not self.db:
            return pd.DataFrame()

        try:
            # Utiliser get_market_data qui existe dans MarketDatabase
            return self.db.get_market_data(limit=hours*2)
        except Exception as e:
            logger.warning(f"Erreur récupération market cap: {e}")
            return pd.DataFrame()

    def get_real_ohlcv_data(self, symbol: str = "BTC/USDT", timeframe: str = "1h", limit: int = 100) -> pd.DataFrame:
        """Récupère les vraies données OHLCV depuis la base"""
        if not self.db:
            return pd.DataFrame()

        try:
            # Utiliser get_ohlcv qui existe dans MarketDatabase
            return self.db.get_ohlcv(symbol, "binance", timeframe, limit=limit)
        except Exception as e:
            logger.warning(f"Erreur récupération OHLCV: {e}")
            return pd.DataFrame()

    # ==================== MÉTHODES DE COMPATIBILITÉ ====================

    def create_line_chart(self, x_data=None, y_data=None, title="Line Chart",
                         xlabel="X", ylabel="Y", color=None) -> QWidget:
        """Crée un graphique linéaire générique pour compatibilité"""
        if x_data is None or y_data is None:
            return self.create_marketcap_chart()

        widget = self.create_figure_widget(figsize=(4, 2.8))
        fig = widget._figure
        ax = fig.add_subplot(111)

        # Utiliser la couleur fournie ou la première de la palette
        line_color = color if color else self.line_colors[0]

        ax.plot(x_data, y_data, color=line_color, linewidth=2.5, alpha=0.9)

        # Style moderne
        self.setup_modern_style(ax, title, xlabel, ylabel)

        fig.tight_layout(pad=0.5)
        widget._canvas.draw()
        return widget

    def update_dark_mode(self, dark_mode: bool):
        """Met à jour le mode sombre/clair"""
        self.dark_mode = dark_mode
        self._setup_theme()

    def create_default_chart(self) -> QWidget:
        """Crée un graphique par défaut pour les containers"""
        return self.create_dominance_chart()

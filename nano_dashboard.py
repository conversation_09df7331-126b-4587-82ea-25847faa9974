from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QScrollArea,
    QTextEdit,
    QFrame,
    QPushButton,
    QApplication,
    QStatusBar,
    QLabel,
    QSizePolicy
)
from PySide6.QtCore import Qt, QTimer
from nano_style import get_scrollbar_style
from PySide6.QtCore import Qt, QMimeData
from PySide6.QtGui import QDrag, QFont, QPixmap, QImage, QPalette
from nano_notifications import NotificationManager
from nano_analyses import IntelligentAnalyzer
import io

class ContentWidget(QWidget):
    """Slot container supporting dark mode, drag & drop, and expand/collapse."""

    def __init__(self, dark_mode=True, parent=None):
        super().__init__(parent)
        self.dark_mode = dark_mode
        self.drag_start_position = None
        self._is_collapsed = False
        self._original_pos = None  # (row, col)
        
        # Définir un ID unique pour le widget
        self.setObjectName('content-widget')
        
        # Activer les attributs de style nécessaires
        self.setAttribute(Qt.WA_StyledBackground, True)
        self.setAttribute(Qt.WA_TranslucentBackground, False)

        lay = QVBoxLayout(self)
        lay.setContentsMargins(8, 8, 8, 8)
        lay.setSpacing(8)

        # Body frame to host content of specialized widgets (SANS header qui pousse)
        self.body = QFrame(self)
        self.body.setLayout(QVBoxLayout())
        self.body.layout().setContentsMargins(0, 0, 0, 0)
        lay.addWidget(self.body)

        # Bouton expand en position ABSOLUE (ne pousse pas le contenu)
        self.toggle_btn = QPushButton("⛶", self)
        self.toggle_btn.setFixedSize(28, 28)
        self.toggle_btn.clicked.connect(self._toggle_expand)

        # Style du bouton avec z-index élevé et position absolue
        self.toggle_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(76, 86, 106, 0.9);
                color: #ECEFF4;
                border: 1px solid #4C566A;
                border-radius: 14px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgba(129, 161, 193, 0.9);
                border-color: #81A1C1;
            }
            QPushButton:pressed {
                background-color: rgba(94, 129, 172, 0.9);
            }
        """)

        # Positionner le bouton en haut à droite (position absolue)
        self.toggle_btn.move(self.width() - 35, 7)  # 7px du haut, 7px de la droite

        # Assurer que le bouton soit toujours au-dessus (z-index élevé)
        self.toggle_btn.raise_()

        # hide expand icon by default; appears on hover
        self.toggle_btn.hide()

        self.update_dark_mode(self.dark_mode)
        self.setAcceptDrops(True)

    def resizeEvent(self, event):
        """Repositionner le bouton expand lors du redimensionnement"""
        super().resizeEvent(event)
        if hasattr(self, 'toggle_btn'):
            # Repositionner le bouton en haut à droite
            self.toggle_btn.move(self.width() - 35, 7)

    # Show/hide expand icon on hover
    def enterEvent(self, event):
        try:
            # If expanded, ensure the close icon is visible
            container = self.parentWidget()
            while container is not None and not hasattr(container, "_expanded_widget"):
                container = container.parentWidget()
            if container is not None and getattr(container, "_expanded_widget", None) is self:
                self.toggle_btn.setText("×")
                self.toggle_btn.show()
            else:
                self.toggle_btn.setText("⛶")
                self.toggle_btn.show()

            # S'assurer que le bouton reste au-dessus de tous les autres widgets
            self.toggle_btn.raise_()
        except Exception:
            pass
        super().enterEvent(event)

    def leaveEvent(self, event):
        try:
            # Keep the close icon visible while expanded; otherwise hide on leave
            container = self.parentWidget()
            while container is not None and not hasattr(container, "_expanded_widget"):
                container = container.parentWidget()
            if container is not None and getattr(container, "_expanded_widget", None) is self:
                self.toggle_btn.setText("×")
                self.toggle_btn.show()
            else:
                self.toggle_btn.hide()
        except Exception:
            pass
        super().leaveEvent(event)

    def update_dark_mode(self, dark_mode: bool):
        """Applique le thème au content widget et notifie le parent (overlay inclus)."""
        # imports locaux pour éviter les cycles
        try:
            from nano_style import get_content_style
        except Exception:
            def get_content_style(_): return ""

        self.dark_mode = bool(dark_mode)

        # Évite le flicker pendant le restylage
        self.setUpdatesEnabled(False)
        try:
            # Styles bouton (si présent)
            fg = "#fff" if self.dark_mode else "#000"
            hover_bg = "rgba(255,255,255,0.08)" if self.dark_mode else "rgba(0,0,0,0.06)"
            btn_style = f"""
                QPushButton {{
                    background: transparent;
                    border: none;
                    color: {fg};
                    font-size: 14px;
                    padding: 2px;
                }}
                QPushButton:hover {{
                    background: {hover_bg};
                    border-radius: 6px;
                }}
            """

            # Reset + styles de base
            self.setStyleSheet("")
            content_style = get_content_style(self.dark_mode)
            self.setStyleSheet(content_style + btn_style)

            if hasattr(self, "toggle_btn") and self.toggle_btn:
                self.toggle_btn.setStyleSheet(btn_style)

            if hasattr(self, "body") and self.body:
                self.body.setStyleSheet("QFrame { background: transparent; }")

            # Propager au chart generator local (si présent)
            if hasattr(self, "chart_generator"):
                try:
                    self.chart_generator.update_dark_mode(self.dark_mode)
                except Exception:
                    pass

            # ⚠️ IMPORTANT : si un overlay est ouvert chez un parent, re-thémer
            parent = self.parent()
            # remonte jusqu’au dashboard qui détient _apply_overlay_theme
            while parent is not None and not hasattr(parent, "_apply_overlay_theme"):
                parent = parent.parent()
            if parent is not None and hasattr(parent, "_apply_overlay_theme"):
                try:
                    parent._apply_overlay_theme()
                except Exception:
                    pass

        finally:
            # Re-polish + rafraîchissement
            try:
                self.style().unpolish(self)
                self.style().polish(self)
            except Exception:
                pass
            self.setUpdatesEnabled(True)
            self.update()



    # Expand/Collapse behavior (simple hide/show of body)
    def _toggle_expand(self):
        # Full expand/collapse over the entire dashboard area (3x4)
        container = self.parentWidget()
        while container is not None and not hasattr(container, "expand_widget"):
            container = container.parentWidget()
        if container is None:
            # fallback to simple collapse of body if no container API
            self._is_collapsed = not self._is_collapsed
            self.body.setVisible(not self._is_collapsed)
            return

        if getattr(container, "_expanded_widget", None) is self:
            container.collapse_widget(self)
            # update icon to expand
            self.toggle_btn.setText("⛶")
        else:
            container.expand_widget(self)
            # update icon to close
            self.toggle_btn.setText("×")

    # Drag & drop to enable swapping between slots
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.position()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if not (event.buttons() & Qt.LeftButton):
            return
        start = getattr(self, "drag_start_position", None)
        if start is None:
            return
        if (event.position() - start).manhattanLength() < QApplication.startDragDistance():
            return

        drag = QDrag(self)
        mime = QMimeData()
        mime.setText(" ")  # sentinel used by container to identify swaps
        drag.setMimeData(mime)
        drag.exec_(Qt.MoveAction)

    def dragEnterEvent(self, event):
        if event.mimeData().hasText() and event.mimeData().text() == " ":
            event.acceptProposedAction()

    def dropEvent(self, event):
        if event.mimeData().hasText() and event.mimeData().text() == " ":
            src = event.source()
            # Find ancestor that knows how to swap
            p = self.parentWidget()
            while p is not None and not hasattr(p, "swap_widgets"):
                p = p.parentWidget()
            if p is not None:
                p.swap_widgets(src, self)
                event.acceptProposedAction()




class NanoDashboard(QWidget):
    def __init__(self, dark_mode: bool = True):
        super().__init__()
        self.dark_mode = dark_mode
        self.contents = {}

        # Scroll container (no border) avec scrollbar personnalisée
        self.scroll = QScrollArea(self)
        self.scroll.setWidgetResizable(True)
        self.scroll.setFrameShape(QFrame.NoFrame)

        # Appliquer le style de base + scrollbar personnalisée
        base_style = "QScrollArea { border: none; } QScrollArea > QWidget > QWidget { background: transparent; }"
        scrollbar_style = get_scrollbar_style(self.dark_mode)
        self.scroll.setStyleSheet(base_style + scrollbar_style)

        # Content widget and grid avec optimisations responsive
        self.content = QWidget()
        self.grid = QGridLayout(self.content)

        # Marges et espacement optimisés pour maximiser l'espace
        self.grid.setContentsMargins(2, 2, 2, 2)
        self.grid.setSpacing(2)

        # Configuration pour éviter le scrolling
        self.content.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.scroll.setWidget(self.content)

        # Désactiver les scrollbars si possible
        self.scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Main layout
        root = QVBoxLayout(self)
        root.setContentsMargins(0, 0, 0, 0)
        root.setSpacing(0)  # Supprime l'espace entre les widgets
        root.addWidget(self.scroll)

        # Create explicit 3x4 slots and mapping
        self._expanded_widget = None
        self._overlay_widget = None  # Widget overlay pour l'expansion
        self._create_slots()
        self._configure_stretch()

        self.update_dark_mode(self.dark_mode)

        # Initialiser l'analyzer et la mise à jour automatique
        self.setup_analyzer_and_updates()



    def _create_slots(self):
        # Fill a 3x4 grid (12 slots) with ContentWidgetWithNanoContainer
        from nano_content_widget import ContentWidgetWithNanoContainer, CONTAINER_CONFIGS

        self.slot_order = []  # ordered list of widgets content1..content12
        self.slot_keys = {}   # widget -> "contentN"
        self.slot_map = {}    # "contentN" -> user-defined name (developer will set later)

        for i in range(12):
            r = i // 4
            c = i % 4

            # Récupérer la configuration pour ce container
            config = CONTAINER_CONFIGS[i] if i < len(CONTAINER_CONFIGS) else {
                "title": f"Container {i+1}",
                "type": "normal",
                "analysis": "generic",
                "status": "neutral"
            }

            # Créer le widget de contenu avec NanoContainer intégré
            w = ContentWidgetWithNanoContainer(self.dark_mode, parent=self, container_config=config)

            # S'assurer que l'ID est bien défini
            w.setObjectName('content-widget')

            # Ajouter le widget à la grille
            self.grid.addWidget(w, r, c)
            self.contents[w] = (r, c)

            # Mettre à jour le style immédiatement
            w.update_dark_mode(self.dark_mode)

            # Configurer les clés et la carte des emplacements
            key = f"content{i+1}"
            self.slot_order.append(w)
            self.slot_keys[w] = key
            self.slot_map[key] = config["title"]  # Utiliser le titre de la config

    def _configure_stretch(self):
        """Configuration optimisée pour afficher 12 panneaux sans scrollbar"""
        # Calcul dynamique des dimensions basé sur la taille de l'écran
        from PySide6.QtWidgets import QApplication
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # Dimensions optimisées pour différentes résolutions
        if screen_geometry.height() >= 1080:  # Full HD et plus
            min_height = 280
            min_width = 320
        elif screen_geometry.height() >= 900:  # Écrans moyens
            min_height = 240
            min_width = 280
        else:  # Écrans plus petits
            min_height = 200
            min_width = 250

        # Configuration des colonnes avec largeur adaptative
        for col in range(4):
            self.grid.setColumnStretch(col, 1)
            self.grid.setColumnMinimumWidth(col, min_width)

        # Configuration des lignes avec hauteur optimisée
        for row in range(3):
            self.grid.setRowStretch(row, 1)
            self.grid.setRowMinimumHeight(row, min_height)

        # Espacement réduit pour maximiser l'espace
        self.grid.setSpacing(3)
        self.grid.setContentsMargins(3, 3, 3, 3)

        print(f"📐 Layout configuré: {min_width}x{min_height} pour écran {screen_geometry.width()}x{screen_geometry.height()}")

    def update_dark_mode(self, dark: bool):
        """Applique le thème au dashboard, aux contenus et à l'overlay ouvert."""
        # imports locaux pour éviter les cycles
        try:
            from nano_style import get_scrollbar_style
        except Exception:
            def get_scrollbar_style(_): return ""

        self.dark_mode = bool(dark)

        # Réduire le flicker
        self.setUpdatesEnabled(False)
        try:
            # Scrollbar
            base_style = (
                "QScrollArea { border: none; } "
                "QScrollArea > QWidget > QWidget { background: transparent; }"
            )
            if hasattr(self, "scroll") and self.scroll:
                scrollbar_style = get_scrollbar_style(self.dark_mode)
                self.scroll.setStyleSheet(base_style + scrollbar_style)

            # Propager à chaque content widget
            for w in list(getattr(self, "contents", {}).keys()):
                try:
                    if hasattr(w, "update_dark_mode"):
                        w.setStyleSheet("")    # reset pour éviter héritages incohérents
                        w.update_dark_mode(self.dark_mode)
                        w.update()
                except Exception as e:
                    print(f"[update_dark_mode] content update error: {e}")

            # ⚠️ IMPORTANT : re-thémer l'overlay si visible
            if hasattr(self, "_apply_overlay_theme"):
                try:
                    self._apply_overlay_theme()
                except Exception as e:
                    print(f"[update_dark_mode] overlay theme error: {e}")

            # Propager aussi au chart_generator global (si tu en as un)
            if hasattr(self, "chart_generator"):
                try:
                    self.chart_generator.update_dark_mode(self.dark_mode)
                except Exception:
                    pass

        finally:
            self.setUpdatesEnabled(True)
            self.update()
            self.repaint()

    # Backward-compatibility shim for callers expecting set_dark_mode()
    def set_dark_mode(self, dark: bool):
        self.update_dark_mode(dark)

    # Public API similar to parent: swap two slot widgets
    def swap_widgets(self, w1: QWidget, w2: QWidget):
        if w1 not in self.contents or w2 not in self.contents:
            return
        r1, c1 = self.contents[w1]
        r2, c2 = self.contents[w2]
        self.grid.addWidget(w1, r2, c2)
        self.grid.addWidget(w2, r1, c1)
        self.contents[w1], self.contents[w2] = (r2, c2), (r1, c1)
        # keep slot_keys order consistent by swapping keys
        if w1 in self.slot_keys and w2 in self.slot_keys:
            self.slot_keys[w1], self.slot_keys[w2] = self.slot_keys[w2], self.slot_keys[w1]
        # also swap in slot_order if present
        try:
            i1 = self.slot_order.index(w1)
            i2 = self.slot_order.index(w2)
            self.slot_order[i1], self.slot_order[i2] = self.slot_order[i2], self.slot_order[i1]
        except ValueError:
            pass



    def expand_widget(self, w: QWidget):
        # Ferme un éventuel overlay déjà ouvert
        if hasattr(self, "_expanded_widget") and self._expanded_widget is not None and self._expanded_widget is not w:
            self.collapse_widget(self._expanded_widget)

        if not hasattr(self, "contents") or w not in self.contents:
            return

        # Palette (fallback si helpers absents)
        try:
            c = self._overlay_colors()
        except Exception:
            c = {
                "backdrop":    "rgba(0,0,0,0.45)",
                "card_bg":     "rgba(255,255,255,0.98)",
                "card_border": "#D1D5DB",
                "text":        "#2C3E50",
            }

        # --- Backdrop overlay (scrim) ---
        self._overlay_widget = QWidget(self)
        self._overlay_widget.setObjectName("OverlayBackdrop")
        self._overlay_widget.setStyleSheet(f"""
            QWidget#OverlayBackdrop {{
                background-color: {c.get('backdrop','rgba(0,0,0,0.45)')};
            }}
        """)
        overlay_layout = QVBoxLayout(self._overlay_widget)
        overlay_layout.setContentsMargins(20, 20, 20, 20)

        # --- Carte (contenu central agrandi) ---
        expanded_content = QFrame()
        expanded_content.setObjectName("OverlayCard")
        expanded_content.setStyleSheet(f"""
            QFrame#OverlayCard {{
                background-color: {c.get('card_bg','rgba(255,255,255,0.98)')};
                border: 1px solid {c.get('card_border','#D1D5DB')};
                border-radius: 12px;
            }}
        """)
        expanded_layout = QVBoxLayout(expanded_content)
        expanded_layout.setSpacing(20)

        # Copier le contenu du nano container
        chart_widget = None
        if hasattr(w, 'get_nano_container'):
            nano_container = w.get_nano_container()
            if nano_container:
                # Header
                header_frame = QFrame()
                header_layout = QHBoxLayout(header_frame)

                title_label = QLabel(f"📊 {nano_container.banner_label.text()}")
                title_label.setObjectName("OverlayTitleLabel")
                title_label.setStyleSheet(f"""
                    QLabel#OverlayTitleLabel {{
                        color: {c.get('text','#2C3E50')};
                        font-size: 28px;
                        font-weight: 700;
                        padding: 15px;
                    }}
                """)
                header_layout.addWidget(title_label)

                if hasattr(nano_container, 'variation_table'):
                    badge = QLabel("📈 Variations récentes")
                    badge.setObjectName("OverlayBadge")
                    badge.setStyleSheet(f"""
                        QLabel#OverlayBadge {{
                            color: {c.get('text','#2C3E50')};
                            font-size: 14px;
                            padding: 10px;
                            border: 1px solid {c.get('card_border','#D1D5DB')};
                            border-radius: 6px;
                            background-color: {c.get('card_bg','rgba(255,255,255,0.98)')};
                        }}
                    """)
                    header_layout.addWidget(badge)

                expanded_layout.addWidget(header_frame)

                # --- Chart snapshot robuste ---
                if hasattr(nano_container, 'current_chart_widget') and nano_container.current_chart_widget:
                    try:
                        original_chart = nano_container.current_chart_widget
                        if hasattr(original_chart, 'figure') and original_chart.figure:
                            import io
                            buf = io.BytesIO()
                            original_chart.figure.savefig(
                                buf, format="png",
                                dpi=original_chart.figure.dpi,
                                transparent=True, bbox_inches="tight"
                            )
                            buf.seek(0)
                            image = QImage.fromData(buf.read(), "PNG")

                            snapshot_label = QLabel()
                            snapshot_label.setObjectName("OverlaySnapshot")
                            snapshot_label.setAlignment(Qt.AlignCenter)

                            # Dimensionnement contrôlé (pas d'upscale)
                            img_w, img_h = image.width(), image.height()
                            avail_w = int(self.width() * 0.78)
                            avail_h = int(self.height() * 0.52)
                            scale = min(avail_w / img_w, avail_h / img_h, 1.0)
                            target_w = max(1, int(img_w * scale))
                            target_h = max(1, int(img_h * scale))

                            pix = QPixmap.fromImage(image).scaled(
                                target_w, target_h,
                                Qt.KeepAspectRatio, Qt.SmoothTransformation
                            )
                            snapshot_label.setPixmap(pix)
                            snapshot_label.setMaximumSize(target_w, target_h)
                            snapshot_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

                            snapshot_label.setStyleSheet(f"""
                                QLabel#OverlaySnapshot {{
                                    background-color: {c.get('card_bg','rgba(255,255,255,0.98)')};
                                    border: 1px solid {c.get('card_border','#D1D5DB')};
                                    border-radius: 8px;
                                    color: {c.get('text','#2C3E50')};
                                    padding: 8px;
                                }}
                            """)
                            expanded_layout.addWidget(snapshot_label)
                            chart_widget = snapshot_label
                            print("✅ Chart snapshot inséré dans l'overlay (taille contrôlée)")
                    except Exception as e:
                        print(f"⚠️ Snapshot overlay échoué: {e}")

                # Fallback si pas de chart disponible
                if chart_widget is None:
                    placeholder = QLabel("📊 Graphique en mode étendu\n\n(Chart indisponible)")
                    placeholder.setObjectName("OverlaySnapshot")
                    placeholder.setAlignment(Qt.AlignCenter)
                    placeholder.setStyleSheet(f"""
                        QLabel#OverlaySnapshot {{
                            color: {c.get('text','#2C3E50')};
                            font-size: 18px;
                            padding: 40px;
                            text-align: center;
                            border: 2px dashed {c.get('card_border','#D1D5DB')};
                            border-radius: 8px;
                            background-color: {c.get('card_bg','rgba(255,255,255,0.98)')};
                            min-height: 300px;
                        }}
                    """)
                    expanded_layout.addWidget(placeholder)

                # Phrase d'analyse
                phrase = nano_container.phrase_label.text() if hasattr(nano_container, 'phrase_label') else ""
                phrase_label = QLabel(phrase)
                phrase_label.setObjectName("OverlayPhrase")
                phrase_label.setAlignment(Qt.AlignCenter)
                phrase_label.setWordWrap(True)
                phrase_label.setStyleSheet(f"""
                    QLabel#OverlayPhrase {{
                        color: {c.get('text','#2C3E50')};
                        font-size: 16px;
                        padding: 20px;
                        background-color: {c.get('card_bg','rgba(255,255,255,0.98)')};
                        border: 1px solid {c.get('card_border','#D1D5DB')};
                        border-radius: 8px;
                    }}
                """)
                expanded_layout.addWidget(phrase_label)

        overlay_layout.addWidget(expanded_content)

        # Bouton de fermeture (couleurs selon thème)
        is_dark = False
        try:
            is_dark = self._is_dark()
        except Exception:
            is_dark = True

        close_btn = QPushButton("✕ Fermer", self._overlay_widget)
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {'#BF616A' if is_dark else '#E11D48'};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {'#D08770' if is_dark else '#F43F5E'};
            }}
        """)
        close_btn.clicked.connect(lambda: self.collapse_widget(w))
        overlay_layout.addWidget(close_btn, alignment=Qt.AlignCenter)

        # Géométrie & affichage
        self._overlay_widget.setGeometry(0, 0, self.width(), self.height())
        self._overlay_widget.show()
        self._overlay_widget.raise_()
        self._overlay_widget.setFocus()

        # État / références pour le re-thémage
        self._expanded_widget = w
        self._overlay_card = expanded_content

        # Thème immédiatement (et pour bascules futures)
        try:
            self._apply_overlay_theme()
        except Exception:
            pass





    def collapse_widget(self, w: QWidget):
        if self._overlay_widget:
            self._overlay_widget.hide()
            self._overlay_widget.deleteLater()
            self._overlay_widget = None

        if self._expanded_widget is w:
            self._expanded_widget = None

    def resizeEvent(self, event):
        """Redimensionner l'overlay quand la fenêtre change de taille"""
        super().resizeEvent(event)
        if self._overlay_widget and self._overlay_widget.isVisible():
            # Redimensionner l'overlay pour couvrir toute la zone du dashboard
            self._overlay_widget.setGeometry(0, 0, self.width(), self.height())

    # Helper to write to terminal
    def display_message_to_terminal(self, title, message, level="info"):
        # broadcast to all slots that support append_message (none named explicitly)
        for w in self.contents.keys():
            if hasattr(w, "append_message"):
                w.append_message(title, message, level)

    def setup_analyzer_and_updates(self):
        """Configure l'analyzer et la mise à jour automatique des containers"""
        try:
            from nano_analyses import MarketAnalyzer
            from nano_processor import DataProcessor

            # Créer le data processor (optionnel)
            try:
                self.data_processor = DataProcessor("market_data.db")
                self.analyzer = MarketAnalyzer(self.data_processor)

                # CONNECTER la mise à jour des widgets aux nouvelles données
                if hasattr(self.data_processor, 'progress_updated'):
                    self.data_processor.progress_updated.connect(self.on_data_processor_update)

                print("✅ Dashboard: Analyzer avec DataProcessor + Signaux connectés")
            except:
                self.analyzer = MarketAnalyzer()
                print("✅ Dashboard: Analyzer standalone")

            # Initialiser l'analyseur intelligent et le gestionnaire de notifications
            self.intelligent_analyzer = IntelligentAnalyzer(self.analyzer)
            self.notification_manager = NotificationManager(self)
            
            # Timer pour mise à jour automatique
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_all_nano_containers)
            self.update_timer.start(30000)  # 30 secondes

            # Timer pour les analyses intelligentes (toutes les 30 secondes pour test)
            self.smart_analysis_timer = QTimer()
            self.smart_analysis_timer.timeout.connect(self.run_smart_analysis)
            self.smart_analysis_timer.start(30000)  # 30 secondes pour test

            # Première mise à jour après 2 secondes
            QTimer.singleShot(2000, self.update_all_nano_containers)
            
            # Première analyse intelligente après 10 secondes
            QTimer.singleShot(10000, self.run_smart_analysis)
            
            # Test immédiat d'une notification (plus rapide)
            QTimer.singleShot(1000, self.test_notification)

        except Exception as e:
            print(f"⚠️ Dashboard: Erreur setup analyzer: {e}")
            self.analyzer = None

    def on_data_processor_update(self, worker_type: str, exchange: str, symbol: str,
                                timeframe: str, percent: int, message: str):
        """Réagit aux mises à jour du data processor et met à jour les widgets"""
        if percent == 100 and worker_type == "MARKET_DATA":
            print(f"🔄 Dashboard: Nouvelles données MARKET_DATA reçues - Mise à jour des widgets")
            # Attendre 500ms pour que la base soit bien mise à jour
            from PySide6.QtCore import QTimer
            QTimer.singleShot(500, self.update_all_nano_containers)

    def update_all_nano_containers(self):
        """Met à jour tous les NanoContainers avec les données de l'analyzer"""
        if not hasattr(self, 'analyzer') or not self.analyzer:
            return

        print("🔄 Dashboard: Mise à jour des NanoContainers...")

        for widget in self.contents.keys():
            if hasattr(widget, 'update_container_data'):
                try:
                    widget.update_container_data(self.analyzer)
                except Exception as e:
                    print(f"Erreur mise à jour container: {e}")

    def get_nano_containers(self):
        """Retourne la liste de tous les NanoContainers"""
        containers = []
        for widget in self.contents.keys():
            if hasattr(widget, 'get_nano_container'):
                nano_container = widget.get_nano_container()
                if nano_container:
                    containers.append(nano_container)
        return containers

    def force_refresh_containers(self):
        """Force la mise à jour immédiate de tous les containers"""
        self.update_all_nano_containers()
        
    def run_smart_analysis(self):
        """Exécute l'analyse intelligente et affiche les notifications importantes"""
        if not hasattr(self, 'intelligent_analyzer') or not self.intelligent_analyzer:
            print("⚠️ IntelligentAnalyzer non initialisé")
            return
            
        if not hasattr(self, 'notification_manager') or not self.notification_manager:
            print("⚠️ NotificationManager non initialisé")
            return
            
        try:
            # Obtenir les prévisions intelligentes
            predictions = self.intelligent_analyzer.get_smart_predictions()
            
            if predictions:
                print(f"🧠 Analyse intelligente: {len(predictions)} alertes générées")
                
                # Ajouter chaque prédiction au gestionnaire de notifications
                for prediction in predictions:
                    print(f"📢 Ajout notification: {prediction.get('title', 'Sans titre')} - Confiance: {prediction.get('confidence', 0)*100:.0f}%")
                    self.notification_manager.add_notification(prediction)
                    
                # Vérifier le nombre de notifications actives
                active_count = self.notification_manager.get_notification_count()
                print(f"📊 Notifications actives: {active_count}")
                
            else:
                print("🧠 Analyse intelligente: Aucune alerte significative détectée")
                
        except Exception as e:
            print(f"⚠️ Erreur lors de l'analyse intelligente: {e}")
            import traceback
            traceback.print_exc()
            
    def test_notification(self):
        """Test d'affichage d'une notification"""
        if not hasattr(self, 'notification_manager') or not self.notification_manager:
            print("⚠️ NotificationManager non disponible pour le test")
            return
            
        test_alert = {
            'type': 'TEST_NOTIFICATION',
            'title': '🧪 TEST NOTIFICATION',
            'message': 'Test du système de notifications. Si vous voyez ceci, le système fonctionne correctement !',
            'confidence': 0.90,
            'priority': 'HIGH',
            'duration': 60
        }
        
        print("🧪 Test notification: Ajout d'une notification de test...")
        self.notification_manager.add_notification(test_alert)
        
        # Vérifier le nombre de notifications
        active_count = self.notification_manager.get_notification_count()
        print(f"📊 Notifications actives après test: {active_count}")
        
    def get_notification_manager(self):
        """Retourne le gestionnaire de notifications"""
        return getattr(self, 'notification_manager', None)



    def _is_dark(self) -> bool:
        """Décide dark/light.
        Ordre: self.dark_mode -> chart_generator.dark_mode -> luminance palette Qt.
        """
        if hasattr(self, "dark_mode"):
            return bool(self.dark_mode)
        cg = getattr(self, "chart_generator", None)
        if cg is not None and hasattr(cg, "dark_mode"):
            return bool(cg.dark_mode)
        pal = QApplication.instance().palette()
        col = pal.color(QPalette.Window)
        # luminance simple
        lum = 0.2126 * col.redF() + 0.7152 * col.greenF() + 0.0722 * col.blueF()
        return lum < 0.5

    def _overlay_colors(self) -> dict:
        if self._is_dark():
            return {
                "backdrop":    "rgba(0,0,0,0.45)",
                "card_bg":     "rgba(46,52,64,0.96)",
                "card_border": "#4C566A",
                "text":        "#2EE1FF" if False else "#E8E8E8",  # tu peux changer l'accent si tu veux
            }
        else:
            return {
                "backdrop":    "rgba(0,0,0,0.25)",
                "card_bg":     "rgba(255,255,255,0.98)",
                "card_border": "#D1D5DB",
                "text":        "#2C3E50",
            }

    def _apply_overlay_theme(self):
        """Applique light/dark à tout l’overlay existant (backdrop, carte, labels)."""
        ow = getattr(self, "_overlay_widget", None)
        if ow is None:
            return
        c = self._overlay_colors()

        # Backdrop
        ow.setObjectName("OverlayBackdrop")
        ow.setStyleSheet(f"QWidget#OverlayBackdrop {{ background-color: {c['backdrop']}; }}")

        # Carte
        card = getattr(self, "_overlay_card", None)
        if card is not None:
            card.setObjectName("OverlayCard")
            card.setStyleSheet(
                "QFrame#OverlayCard {"
                f"background-color: {c['card_bg']};"
                f"border: 1px solid {c['card_border']};"
                "border-radius: 12px;"
                "}"
            )

        # Sous-widgets marqués par objectName
        def style_lbl(w, qss):
            if w: w.setStyleSheet(qss)

        title = ow.findChild(type(card), "OverlayTitle")  # QFrame ? (ignoré)
        title_lbl = ow.findChild(type(ow), "OverlayTitleLabel")  # QLabel

        badge = ow.findChild(type(ow), "OverlayBadge")           # QLabel
        snap  = ow.findChild(type(ow), "OverlaySnapshot")        # QLabel
        phrase= ow.findChild(type(ow), "OverlayPhrase")          # QLabel

        if title_lbl:
            style_lbl(title_lbl,
                "QLabel#OverlayTitleLabel {"
                f"color: {c['text']}; font-size: 28px; font-weight: 700; padding: 15px;"
                "}"
            )
        if badge:
            style_lbl(badge,
                "QLabel#OverlayBadge {"
                f"color: {c['text']}; font-size: 14px; padding: 10px;"
                f"border: 1px solid {c['card_border']}; border-radius: 6px;"
                f"background-color: {c['card_bg']};"
                "}"
            )
        if snap:
            style_lbl(snap,
                "QLabel#OverlaySnapshot {"
                f"background-color: {c['card_bg']};"
                f"border: 1px solid {c['card_border']}; border-radius: 8px;"
                f"color: {c['text']}; padding: 8px;"
                "}"
            )
        if phrase:
            style_lbl(phrase,
                "QLabel#OverlayPhrase {"
                f"color: {c['text']}; font-size: 16px; padding: 20px;"
                f"background-color: {c['card_bg']};"
                f"border: 1px solid {c['card_border']}; border-radius: 8px;"
                "text-align: center;"
                "}"
            )


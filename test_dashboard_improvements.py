#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour valider les améliorations du dashboard
- Test des graphiques Plotly vs Matplotlib
- Test du layout responsive
- Test du système de notifications
- Test de l'audit des données
- Test de performance
"""

import sys
import os
import unittest
import time
from unittest.mock import Mock, patch
from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtCore import QTimer
from PySide6.QtTest import QTest

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestDashboardImprovements(unittest.TestCase):
    """Tests pour les améliorations du dashboard"""
    
    @classmethod
    def setUpClass(cls):
        """Configuration initiale des tests"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """Configuration avant chaque test"""
        self.mock_analyzer = Mock()
        self.mock_analyzer.db = Mock()
        self.mock_analyzer.db.get_latest_market_data.return_value = self._create_mock_data()
    
    def _create_mock_data(self):
        """Crée des données de test"""
        import pandas as pd
        from datetime import datetime, timedelta
        
        dates = [datetime.now() - timedelta(hours=i) for i in range(24)]
        data = {
            'created_at': dates,
            'btc_dom': [56.2 + i * 0.1 for i in range(24)],
            'eth_dom': [18.3 + i * 0.05 for i in range(24)],
            'alt_dom': [25.5 - i * 0.15 for i in range(24)],
            'vol_global': [1e12 + i * 1e10 for i in range(24)]
        }
        return pd.DataFrame(data)
    
    def test_plotly_chart_generation(self):
        """Test la génération de graphiques Plotly"""
        try:
            from nano_plotly_charts import PlotlyChartGenerator
            
            generator = PlotlyChartGenerator(dark_mode=True)
            
            # Test donut chart
            widget = generator.create_dominance_donut_chart()
            self.assertIsNotNone(widget)
            self.assertTrue(hasattr(widget, '_figure'))
            
            # Test volume chart
            widget = generator.create_volume_chart()
            self.assertIsNotNone(widget)
            
            print("✅ Test Plotly: Génération de graphiques réussie")
            
        except ImportError:
            self.skipTest("Plotly non disponible")
        except Exception as e:
            self.fail(f"Erreur génération Plotly: {e}")
    
    def test_matplotlib_fallback(self):
        """Test le fallback vers Matplotlib"""
        try:
            from nano_matplotlib_charts import MatplotlibChartGenerator
            
            generator = MatplotlibChartGenerator(dark_mode=True)
            
            # Test création widget
            widget = generator.create_figure_widget()
            self.assertIsNotNone(widget)
            
            print("✅ Test Matplotlib: Fallback fonctionnel")
            
        except Exception as e:
            self.fail(f"Erreur Matplotlib: {e}")
    
    def test_layout_optimization(self):
        """Test l'optimisation du layout"""
        try:
            from nano_dashboard import NanoDashboard
            
            dashboard = NanoDashboard(dark_mode=True)
            
            # Vérifier que le layout est configuré
            self.assertIsNotNone(dashboard.grid)
            
            # Vérifier les dimensions minimales
            for row in range(3):
                min_height = dashboard.grid.rowMinimumHeight(row)
                self.assertGreater(min_height, 0)
                self.assertLessEqual(min_height, 300)  # Pas trop grand
            
            for col in range(4):
                min_width = dashboard.grid.columnMinimumWidth(col)
                self.assertGreater(min_width, 0)
                self.assertLessEqual(min_width, 400)  # Pas trop grand
            
            print("✅ Test Layout: Optimisation réussie")
            
        except Exception as e:
            self.fail(f"Erreur layout: {e}")
    
    def test_notification_system(self):
        """Test le système de notifications amélioré"""
        try:
            from nano_notifications import SmartNotificationWidget, NotificationManager
            
            # Test création notification
            alert_data = {
                'title': 'Test',
                'message': 'Message de test',
                'priority': 'HIGH',
                'duration': 5
            }
            
            notification = SmartNotificationWidget(alert_data)
            self.assertIsNotNone(notification)
            
            # Test gestionnaire
            manager = NotificationManager()
            manager.show_notification(alert_data)
            
            print("✅ Test Notifications: Système amélioré fonctionnel")
            
        except Exception as e:
            self.fail(f"Erreur notifications: {e}")
    
    def test_data_audit_system(self):
        """Test le système d'audit des données"""
        try:
            from nano_data_audit import DataDisplayAuditor
            
            auditor = DataDisplayAuditor()
            
            # Fonction de test
            def test_function(analyzer):
                time.sleep(0.1)  # Simuler du travail
                return "success"
            
            # Auditer la fonction
            result = auditor.audit_function(test_function, self.mock_analyzer)
            
            self.assertIsNotNone(result)
            self.assertTrue(result.success)
            self.assertGreater(result.execution_time, 0)
            
            # Test rapport de santé
            report = auditor.get_function_health_report('test_function')
            self.assertIn('status', report)
            
            print("✅ Test Audit: Système d'audit fonctionnel")
            
        except Exception as e:
            self.fail(f"Erreur audit: {e}")
    
    def test_container_integration(self):
        """Test l'intégration des containers avec les nouveaux systèmes"""
        try:
            from nano_container import NanoContainer
            
            # Test création container
            container = NanoContainer(dark_mode=True)
            self.assertIsNotNone(container)
            
            # Vérifier que le système de graphiques est configuré
            self.assertTrue(hasattr(container, 'chart_engine'))
            
            print("✅ Test Container: Intégration réussie")
            
        except Exception as e:
            self.fail(f"Erreur container: {e}")
    
    def test_performance_benchmarks(self):
        """Test les performances des améliorations"""
        try:
            from nano_content_widget import ContentWidgetWithNanoContainer
            
            # Test création widget
            start_time = time.time()
            widget = ContentWidgetWithNanoContainer(dark_mode=True)
            creation_time = time.time() - start_time
            
            self.assertLess(creation_time, 2.0, "Création widget trop lente")
            
            # Test mise à jour
            start_time = time.time()
            widget.update_content(self.mock_analyzer)
            update_time = time.time() - start_time
            
            self.assertLess(update_time, 5.0, "Mise à jour trop lente")
            
            print(f"✅ Test Performance: Création {creation_time:.3f}s, Mise à jour {update_time:.3f}s")
            
        except Exception as e:
            self.fail(f"Erreur performance: {e}")
    
    def test_error_handling(self):
        """Test la gestion d'erreurs robuste"""
        try:
            from nano_content_widget import ContentWidgetWithNanoContainer
            
            # Créer un analyzer défaillant
            broken_analyzer = Mock()
            broken_analyzer.db = None
            broken_analyzer.get_dominance_data.side_effect = Exception("Test error")
            
            # Le widget doit gérer l'erreur sans crash
            widget = ContentWidgetWithNanoContainer(dark_mode=True)
            
            # Cette opération ne doit pas lever d'exception
            try:
                widget.update_content(broken_analyzer)
                print("✅ Test Erreurs: Gestion robuste des erreurs")
            except Exception as e:
                self.fail(f"Gestion d'erreur défaillante: {e}")
                
        except Exception as e:
            self.fail(f"Erreur test gestion d'erreurs: {e}")

class TestIntegrationComplete(unittest.TestCase):
    """Tests d'intégration complète"""
    
    @classmethod
    def setUpClass(cls):
        """Configuration pour les tests d'intégration"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def test_full_dashboard_startup(self):
        """Test le démarrage complet du dashboard avec toutes les améliorations"""
        try:
            # Simuler le démarrage complet
            from nano_dashboard import NanoDashboard
            from nano_analyses import MarketAnalyzer
            
            # Créer le dashboard
            dashboard = NanoDashboard(dark_mode=True)
            
            # Créer un analyzer de test
            analyzer = Mock()
            analyzer.db = Mock()
            analyzer.db.get_latest_market_data.return_value = pd.DataFrame()
            
            # Tester la mise à jour de tous les widgets
            for i in range(12):
                widget = dashboard.content_widgets[i]
                if hasattr(widget, 'update_content'):
                    widget.update_content(analyzer)
            
            print("✅ Test Intégration: Dashboard complet fonctionnel")
            
        except Exception as e:
            self.fail(f"Erreur intégration complète: {e}")

def run_all_tests():
    """Lance tous les tests"""
    print("🧪 Lancement des tests d'amélioration du dashboard...")
    print("=" * 60)
    
    # Créer la suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter les tests
    suite.addTests(loader.loadTestsFromTestCase(TestDashboardImprovements))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegrationComplete))
    
    # Lancer les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Résumé
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 Tous les tests sont passés avec succès!")
        print("✅ Les améliorations du dashboard sont fonctionnelles")
    else:
        print(f"❌ {len(result.failures)} échecs, {len(result.errors)} erreurs")
        print("⚠️ Certaines améliorations nécessitent des corrections")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de déploiement des améliorations du dashboard
- Vérifie les dépendances
- Configure les nouveaux systèmes
- Migre les configurations existantes
- Lance les tests de validation
"""

import os
import sys
import subprocess
import json
from pathlib import Path

class DashboardDeployment:
    """Gestionnaire de déploiement des améliorations"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backup_dir = self.project_root / "backup_before_improvements"
        self.config_file = self.project_root / "nano_config.json"
        
    def check_dependencies(self):
        """Vérifie et installe les dépendances nécessaires"""
        print("🔍 Vérification des dépendances...")
        
        required_packages = [
            "plotly>=5.0.0",
            "PySide6>=6.0.0",
            "pandas>=1.3.0",
            "numpy>=1.20.0",
            "matplotlib>=3.5.0"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                package_name = package.split(">=")[0]
                __import__(package_name)
                print(f"✅ {package_name} disponible")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package_name} manquant")
        
        if missing_packages:
            print(f"\n📦 Installation des packages manquants...")
            for package in missing_packages:
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    print(f"✅ {package} installé")
                except subprocess.CalledProcessError as e:
                    print(f"❌ Erreur installation {package}: {e}")
                    return False
        
        return True
    
    def backup_existing_files(self):
        """Sauvegarde les fichiers existants"""
        print("💾 Sauvegarde des fichiers existants...")
        
        files_to_backup = [
            "nano_matplotlib_charts.py",
            "nano_dashboard.py",
            "nano_notifications.py",
            "nano_container.py",
            "nano_content_widget.py"
        ]
        
        if not self.backup_dir.exists():
            self.backup_dir.mkdir()
        
        for file_name in files_to_backup:
            source = self.project_root / file_name
            if source.exists():
                destination = self.backup_dir / file_name
                try:
                    import shutil
                    shutil.copy2(source, destination)
                    print(f"✅ {file_name} sauvegardé")
                except Exception as e:
                    print(f"⚠️ Erreur sauvegarde {file_name}: {e}")
    
    def update_configuration(self):
        """Met à jour la configuration pour les nouvelles fonctionnalités"""
        print("⚙️ Mise à jour de la configuration...")
        
        # Configuration par défaut pour les améliorations
        improvements_config = {
            "charts": {
                "preferred_engine": "plotly",
                "fallback_engine": "matplotlib",
                "enable_interactivity": True,
                "responsive_design": True
            },
            "layout": {
                "optimize_for_12_panels": True,
                "adaptive_dimensions": True,
                "minimal_margins": True
            },
            "notifications": {
                "modern_animations": True,
                "adaptive_duration": True,
                "priority_styling": True
            },
            "data_audit": {
                "enable_monitoring": True,
                "performance_thresholds": {
                    "execution_time_warning": 2.0,
                    "execution_time_critical": 5.0,
                    "data_quality_minimum": 0.7
                }
            }
        }
        
        # Charger la configuration existante
        existing_config = {}
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
                print("✅ Configuration existante chargée")
            except Exception as e:
                print(f"⚠️ Erreur lecture config: {e}")
        
        # Fusionner les configurations
        existing_config.update(improvements_config)
        
        # Sauvegarder la nouvelle configuration
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, indent=2, ensure_ascii=False)
            print("✅ Configuration mise à jour")
            return True
        except Exception as e:
            print(f"❌ Erreur sauvegarde config: {e}")
            return False
    
    def run_tests(self):
        """Lance les tests de validation"""
        print("🧪 Lancement des tests de validation...")
        
        test_file = self.project_root / "test_dashboard_improvements.py"
        if not test_file.exists():
            print("⚠️ Fichier de tests non trouvé")
            return False
        
        try:
            result = subprocess.run([
                sys.executable, str(test_file)
            ], capture_output=True, text=True, cwd=self.project_root)
            
            print("📋 Résultats des tests:")
            print(result.stdout)
            
            if result.stderr:
                print("⚠️ Erreurs:")
                print(result.stderr)
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ Erreur exécution tests: {e}")
            return False
    
    def verify_improvements(self):
        """Vérifie que toutes les améliorations sont en place"""
        print("🔍 Vérification des améliorations...")
        
        checks = [
            ("nano_plotly_charts.py", "Système de graphiques Plotly"),
            ("nano_data_audit.py", "Système d'audit des données"),
            ("test_dashboard_improvements.py", "Tests de validation")
        ]
        
        all_good = True
        
        for file_name, description in checks:
            file_path = self.project_root / file_name
            if file_path.exists():
                print(f"✅ {description}")
            else:
                print(f"❌ {description} manquant")
                all_good = False
        
        # Vérifier les modifications dans les fichiers existants
        modifications = [
            ("nano_matplotlib_charts.py", "setup_matplotlib_backend"),
            ("nano_dashboard.py", "_configure_stretch"),
            ("nano_notifications.py", "setup_animations"),
            ("nano_container.py", "PLOTLY_AVAILABLE"),
            ("nano_content_widget.py", "audit_data_function")
        ]
        
        for file_name, expected_content in modifications:
            file_path = self.project_root / file_name
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    if expected_content in content:
                        print(f"✅ {file_name} modifié correctement")
                    else:
                        print(f"⚠️ {file_name} modifications incomplètes")
                        all_good = False
                except Exception as e:
                    print(f"❌ Erreur vérification {file_name}: {e}")
                    all_good = False
            else:
                print(f"❌ {file_name} non trouvé")
                all_good = False
        
        return all_good
    
    def create_startup_script(self):
        """Crée un script de démarrage optimisé"""
        print("🚀 Création du script de démarrage optimisé...")
        
        startup_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de démarrage optimisé pour le dashboard amélioré
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire du projet au path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Point d'entrée principal avec vérifications"""
    print("🚀 Démarrage du NanoMarketSensor amélioré...")
    
    # Vérifier les dépendances critiques
    try:
        import PySide6
        print("✅ PySide6 disponible")
    except ImportError:
        print("❌ PySide6 non disponible - installation requise")
        return 1
    
    # Vérifier Plotly (optionnel)
    try:
        import plotly
        print("✅ Plotly disponible - graphiques interactifs activés")
    except ImportError:
        print("⚠️ Plotly non disponible - fallback vers matplotlib")
    
    # Lancer l'application
    try:
        from main import main as app_main
        return app_main()
    except Exception as e:
        print(f"❌ Erreur démarrage application: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
        
        startup_file = self.project_root / "start_improved_dashboard.py"
        try:
            with open(startup_file, 'w', encoding='utf-8') as f:
                f.write(startup_script)
            
            # Rendre exécutable sur Unix
            if os.name != 'nt':
                os.chmod(startup_file, 0o755)
            
            print(f"✅ Script de démarrage créé: {startup_file}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur création script: {e}")
            return False
    
    def deploy(self):
        """Lance le déploiement complet"""
        print("🚀 Déploiement des améliorations du dashboard")
        print("=" * 60)
        
        steps = [
            ("Vérification des dépendances", self.check_dependencies),
            ("Sauvegarde des fichiers", self.backup_existing_files),
            ("Mise à jour configuration", self.update_configuration),
            ("Vérification des améliorations", self.verify_improvements),
            ("Tests de validation", self.run_tests),
            ("Création script de démarrage", self.create_startup_script)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            try:
                if not step_func():
                    print(f"❌ Échec: {step_name}")
                    return False
                print(f"✅ Réussi: {step_name}")
            except Exception as e:
                print(f"❌ Erreur {step_name}: {e}")
                return False
        
        print("\n" + "=" * 60)
        print("🎉 Déploiement terminé avec succès!")
        print("\n📋 Résumé des améliorations:")
        print("  • ✅ Graphiques Plotly interactifs")
        print("  • ✅ Layout optimisé pour 12 panneaux")
        print("  • ✅ Notifications modernes avec animations")
        print("  • ✅ Système d'audit des données")
        print("  • ✅ Gestion d'erreurs robuste")
        print("  • ✅ Tests de validation automatisés")
        
        print(f"\n🚀 Pour démarrer: python start_improved_dashboard.py")
        print(f"📁 Sauvegarde dans: {self.backup_dir}")
        
        return True

def main():
    """Point d'entrée principal"""
    deployer = DashboardDeployment()
    success = deployer.deploy()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

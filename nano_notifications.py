"""
Système de notifications intelligentes pour NanoMarketSensor
Affiche les alertes et prévisions importantes basées sur l'analyse croisée des indicateurs.
"""

from PySide6.QtWidgets import QWidget, QLabel, QPushButton, QHBoxLayout, QVBoxLayout, QFrame
from PySide6.QtCore import QTimer, Signal, Qt, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QPalette
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

class SmartNotificationWidget(QFrame):
    """Widget de notification intelligent avec animation et fermeture automatique"""
    
    notification_closed = Signal()
    
    def __init__(self, alert_data: Dict, parent=None):
        super().__init__(parent)
        self.alert_data = alert_data
        self.setup_ui()
        self.setup_animations()
        self.setup_auto_close()
        
    def setup_ui(self):
        """Configure l'interface moderne de la notification"""
        # Dimensions optimisées et responsive
        self.setFixedHeight(160)  # Hauteur réduite pour plus d'élégance
        self.setMinimumWidth(380)
        self.setMaximumWidth(520)

        # Styles modernes avec dégradés et transparence
        priority_styles = {
            'HIGH': {
                'background': "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(191, 97, 106, 0.95), stop:1 rgba(208, 135, 112, 0.95))",
                'border': "#BF616A",
                'text_color': '#ECEFF4',
                'accent': '#D08770'
            },
            'MEDIUM': {
                'background': "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(235, 203, 139, 0.95), stop:1 rgba(163, 190, 140, 0.95))",
                'border': "#EBCB8B",
                'text_color': '#2E3440',
                'accent': '#A3BE8C'
            },
            'LOW': {
                'background': "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(136, 192, 208, 0.95), stop:1 rgba(129, 161, 193, 0.95))",
                'border': "#88C0D0",
                'text_color': '#2E3440',
                'accent': '#81A1C1'
            }
        }
        
        style = priority_styles.get(self.alert_data.get('priority', 'LOW'))
        
        # Style moderne avec effets visuels avancés
        self.setStyleSheet(f"""
            QFrame {{
                background: {style['background']};
                border: 2px solid {style['border']};
                border-radius: 16px;
                margin: 3px;
            }}
            QFrame:hover {{
                border: 2px solid {style['accent']};
                transform: translateY(-2px);
            }}
            QLabel {{
                color: {style['text_color']};
                background: transparent;
                padding: 8px;
                line-height: 1.5;
                font-weight: 500;
            }}
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {style['border']}, stop:1 {style['accent']});
                color: white;
                border: none;
                border-radius: 16px;
                font-weight: bold;
                min-width: 32px;
                max-width: 32px;
                min-height: 32px;
                max-height: 32px;
                font-size: 18px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {style['accent']}, stop:1 {style['border']});
                transform: scale(1.1);
            }}
            QPushButton:pressed {{
                transform: scale(0.95);
            }}
        """)
        
        # Layout principal
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(16, 14, 14, 14)
        main_layout.setSpacing(12)
        
        # Layout pour le contenu
        content_layout = QVBoxLayout()
        content_layout.setSpacing(4)
        
        # Titre
        title_label = QLabel(self.alert_data.get('title', 'Alerte'))
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(14)
        title_label.setFont(title_font)
        content_layout.addWidget(title_label)
        
        # Message
        message_label = QLabel(self.alert_data.get('message', ''))
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        message_font = QFont()
        message_font.setPointSize(11)
        message_label.setFont(message_font)
        content_layout.addWidget(message_label)
        
        # Confiance et temps
        info_text = f"Confiance: {self.alert_data.get('confidence', 0)*100:.0f}% • {datetime.now().strftime('%H:%M')}"
        info_label = QLabel(info_text)
        info_font = QFont()
        info_font.setPointSize(10)
        info_font.setItalic(True)
        info_label.setFont(info_font)
        info_label.setStyleSheet("color: #bdc3c7;")
        content_layout.addWidget(info_label)
        
        main_layout.addLayout(content_layout, 1)
        
        # Bouton fermer
        close_button = QPushButton("×")
        close_button.clicked.connect(self.close_notification)
        main_layout.addWidget(close_button)
        
    def setup_animations(self):
        """Configure les animations fluides et modernes"""
        # Animation d'entrée avec effet de rebond
        self.fade_in_animation = QPropertyAnimation(self, b"geometry")
        self.fade_in_animation.setDuration(400)
        self.fade_in_animation.setEasingCurve(QEasingCurve.Type.OutBack)

        # Animation de sortie fluide
        self.fade_out_animation = QPropertyAnimation(self, b"geometry")
        self.fade_out_animation.setDuration(250)
        self.fade_out_animation.setEasingCurve(QEasingCurve.Type.InQuart)
        self.fade_out_animation.finished.connect(self.notification_closed.emit)

        # Animation d'opacité pour l'effet de fondu
        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(300)
        
    def setup_auto_close(self):
        """Configure la fermeture automatique intelligente"""
        # Durée adaptative selon la priorité
        base_duration = self.alert_data.get('duration', 60)
        priority = self.alert_data.get('priority', 'LOW')

        # Ajustement de la durée selon la priorité
        duration_multipliers = {'HIGH': 1.5, 'MEDIUM': 1.2, 'LOW': 1.0}
        final_duration = int(base_duration * duration_multipliers.get(priority, 1.0) * 1000)

        self.auto_close_timer = QTimer()
        self.auto_close_timer.setSingleShot(True)
        self.auto_close_timer.timeout.connect(self.close_notification)
        self.auto_close_timer.start(final_duration)

        # Indicateur visuel de progression (optionnel)
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_progress)
        self.progress_timer.start(100)  # Mise à jour toutes les 100ms
        self.start_time = datetime.now()
        self.total_duration = final_duration / 1000.0

    def update_progress(self):
        """Met à jour l'indicateur de progression visuel"""
        if hasattr(self, 'start_time') and hasattr(self, 'total_duration'):
            elapsed = (datetime.now() - self.start_time).total_seconds()
            progress = min(1.0, elapsed / self.total_duration)

            # Effet de fondu progressif
            opacity = 1.0 - (progress * 0.3)  # Réduction progressive de l'opacité
            self.setWindowOpacity(opacity)
        
    def show_with_animation(self, target_rect: QRect):
        """Affiche la notification avec animation"""
        # Position initiale (hors écran à droite)
        start_rect = QRect(target_rect.x() + 400, target_rect.y(), target_rect.width(), target_rect.height())
        self.setGeometry(start_rect)
        self.show()
        
        # Animation vers la position finale
        self.fade_in_animation.setStartValue(start_rect)
        self.fade_in_animation.setEndValue(target_rect)
        self.fade_in_animation.start()
        
    def close_notification(self):
        """Ferme la notification avec animation"""
        if hasattr(self, 'auto_close_timer'):
            self.auto_close_timer.stop()
            
        # Animation de sortie
        current_rect = self.geometry()
        end_rect = QRect(current_rect.x() + 400, current_rect.y(), current_rect.width(), current_rect.height())
        
        self.fade_out_animation.setStartValue(current_rect)
        self.fade_out_animation.setEndValue(end_rect)
        self.fade_out_animation.start()


class NotificationManager(QWidget):
    """Gestionnaire des notifications intelligentes"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.active_notifications = []
        self.notification_queue = []
        self.max_notifications = 3
        self.notification_spacing = 130  # Espacement augmenté pour les notifications plus hautes
        
        # Timer pour traiter la queue
        self.queue_timer = QTimer()
        self.queue_timer.timeout.connect(self.process_queue)
        self.queue_timer.start(2000)  # Vérifier toutes les 2 secondes
        
        logging.info("NotificationManager initialisé")
        
    def add_notification(self, alert_data: Dict):
        """Ajoute une notification à la queue"""
        # Éviter les doublons récents
        current_time = datetime.now()
        for existing in self.notification_queue + self.active_notifications:
            if (hasattr(existing, 'alert_data') and 
                existing.alert_data.get('type') == alert_data.get('type') and
                (current_time - existing.alert_data.get('created_at', current_time)).seconds < 300):  # 5 minutes
                return
                
        # Ajouter à la queue
        alert_data['created_at'] = current_time
        self.notification_queue.append(alert_data)
        logging.info(f"Notification ajoutée à la queue: {alert_data.get('title', 'Sans titre')}")
        
    def process_queue(self):
        """Traite la queue des notifications"""
        if not self.notification_queue or len(self.active_notifications) >= self.max_notifications:
            return
            
        if not self.parent_widget or not self.parent_widget.isVisible():
            return
            
        # Prendre la prochaine notification
        alert_data = self.notification_queue.pop(0)
        self.show_notification(alert_data)
        
    def show_notification(self, alert_data: Dict):
        """Affiche une notification"""
        try:
            notification = SmartNotificationWidget(alert_data, self.parent_widget)
            notification.notification_closed.connect(lambda: self.remove_notification(notification))
            
            # Calculer la position
            parent_rect = self.parent_widget.rect()
            notification_rect = QRect(
                parent_rect.width() - 570,  # Position à droite ajustée
                50 + len(self.active_notifications) * self.notification_spacing,  # Empiler verticalement
                550,
                120
            )
            
            # Ajouter à la liste active
            self.active_notifications.append(notification)
            
            # Afficher avec animation
            notification.show_with_animation(notification_rect)
            
            logging.info(f"Notification affichée: {alert_data.get('title', 'Sans titre')}")
            
        except Exception as e:
            logging.error(f"Erreur lors de l'affichage de la notification: {e}")
            
    def remove_notification(self, notification):
        """Supprime une notification de la liste active"""
        if notification in self.active_notifications:
            self.active_notifications.remove(notification)
            notification.deleteLater()
            
            # Réorganiser les notifications restantes
            self.reorganize_notifications()
            
    def reorganize_notifications(self):
        """Réorganise les notifications après suppression"""
        if not self.parent_widget:
            return
            
        parent_rect = self.parent_widget.rect()
        
        for i, notification in enumerate(self.active_notifications):
            new_rect = QRect(
                parent_rect.width() - 570,
                50 + i * self.notification_spacing,
                550,
                120
            )
            
            # Animation vers la nouvelle position
            animation = QPropertyAnimation(notification, b"geometry")
            animation.setDuration(200)
            animation.setStartValue(notification.geometry())
            animation.setEndValue(new_rect)
            animation.start()
            
    def clear_all_notifications(self):
        """Ferme toutes les notifications"""
        for notification in self.active_notifications[:]:
            notification.close_notification()
            
        self.notification_queue.clear()
        
    def get_notification_count(self) -> int:
        """Retourne le nombre de notifications actives"""
        return len(self.active_notifications)
